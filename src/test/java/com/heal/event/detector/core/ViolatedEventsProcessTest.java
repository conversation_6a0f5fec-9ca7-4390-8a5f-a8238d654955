package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.AggregatedKpiProtos;
import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.google.protobuf.ProtocolStringList;
import com.heal.configuration.enums.OperationType;
import com.heal.configuration.pojos.CompInstKpiEntity;
import com.heal.configuration.pojos.KpiDetails;
import com.heal.configuration.pojos.KpiViolationConfig;
import com.heal.event.detector.pojos.BasicKPIStateInfo;
import com.heal.event.detector.pojos.MetaData;
import com.heal.event.detector.pojos.ViolatedData;
import com.heal.event.detector.util.ProtoCreator;
import com.heal.event.detector.utility.Constants;
import com.heal.event.detector.utility.cache.CacheWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.lang.reflect.Method;
import java.util.ArrayList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;




class ViolatedEventsProcessTest {

    @Spy
    private ViolatedEventsProcess violatedEventsProcess;

    @Autowired
    ProtoCreator protoCreator;

    @Mock
    CacheWrapper cacheWrapperMock;

    @BeforeEach
    void setUp() {
        violatedEventsProcess = new ViolatedEventsProcess();
    }

    public CompInstKpiEntity getInstanceDetails(int instanceId, String attributeName, int kpiId, Map<String, Double> thresholdMap,
                                                String operation, int severity, int generateAnomaly, int excludeMaintenance) {
        List<KpiViolationConfig> kpiViolationConfigList = new ArrayList<>();
        kpiViolationConfigList.add(KpiViolationConfig.builder()
                .operation(operation)
                .minThreshold(thresholdMap.get("Lower"))
                .maxThreshold(thresholdMap.get("Upper"))
                .severity(severity)
                .generateAnomaly(generateAnomaly)
                .excludeMaintenance(excludeMaintenance)
                .kpiId(kpiId)
                .compInstanceId(instanceId)
                .build());
        Map<String, List<KpiViolationConfig>> kpiViolationConfigMap = new HashMap<>();
        kpiViolationConfigMap.put(attributeName, kpiViolationConfigList);

        return CompInstKpiEntity.builder()
                .kpiViolationConfig(kpiViolationConfigMap)
                .build();
    }

    @Test
    void testViolatedKpi_validConfig() throws Exception {
         ViolatedEventsProcess process = new ViolatedEventsProcess();
//        ViolatedEventsProcess process = Mockito.spy(new ViolatedEventsProcess());
        String accountIdentifier =  "demo";
        int kpiId = 1;
        String instanceIdentifier = "1fa5190c-ce1f-4da0-9f72-51e7d70f73ca";

        String value = "55";

        ProtocolStringList mockAppIdList = Mockito.mock(ProtocolStringList.class);
        Mockito.when(mockAppIdList.get(0)).thenReturn("app1");
        Mockito.when(mockAppIdList.size()).thenReturn(1);
        ProtocolStringList serviceIdList = Mockito.mock(ProtocolStringList.class);
        Mockito.when(serviceIdList.get(0)).thenReturn("svc1");
        Mockito.when(serviceIdList.size()).thenReturn(1);

        // Create a dummy AggregatedKpiProtos.AggregatedKpi and its inner KpiData
        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = Mockito.mock(AggregatedKpiProtos.AggregatedKpi.class);
        KPIAgentMessageProtos.KPIAgentMessage.KpiData kpiData = Mockito.mock(KPIAgentMessageProtos.KPIAgentMessage.KpiData.class);
        Mockito.when(aggregatedKpi.getKpiData()).thenReturn(kpiData);
        Mockito.when(aggregatedKpi.getAccountId()).thenReturn(accountIdentifier);
        Mockito.when(aggregatedKpi.getInstanceId()).thenReturn(instanceIdentifier);
        Mockito.when(aggregatedKpi.getApplicationIdList()).thenReturn(mockAppIdList);
        Mockito.when(aggregatedKpi.getServiceIdList()).thenReturn(serviceIdList);
        Mockito.when(aggregatedKpi.getTimeZoneOffsetInSec()).thenReturn(0);
        Mockito.when(aggregatedKpi.getMetaDataMap()).thenReturn(new HashMap<>());
        Mockito.when(kpiData.getKpiUid()).thenReturn(1);
        Mockito.when(kpiData.getVal()).thenReturn(value);
        Mockito.when(kpiData.getKpiType()).thenReturn(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);
        Mockito.when(kpiData.getIsKpiGroup()).thenReturn(false);
        Mockito.when(kpiData.getTimeInGMT()).thenReturn("2025-05-21 10:00:00");

        // Create a non-empty violation configuration map for attribute "ALL"
        String attributeName = "ALL";
        List<KpiViolationConfig> configList = new ArrayList<>();
        // TO DO: create separate test case
        // Test case for operation "greater than"
        // KpiViolationConfig config = KpiViolationConfig.builder()
        //         .operation("greater than")
        //         .minThreshold(60.0)
        //         .maxThreshold(0D)
        //         .status(1)
        //         .generateAnomaly(1)
        //         .thresholdSeverity("Low")
        //         .thresholdSeverityId(431)
        //         .build();
        // configList.add(config);
        // config = KpiViolationConfig.builder()
        //         .operation("greater than")
        //         .minThreshold(70.0)
        //         .maxThreshold(0D)
        //         .status(1)
        //         .generateAnomaly(1)
        //         .thresholdSeverity("Medium")
        //         .thresholdSeverityId(432)
        //         .build();
        // configList.add(config);
        // config = KpiViolationConfig.builder()
        //         .operation("greater than")
        //         .minThreshold(90.0)
        //         .maxThreshold(0D)
        //         .status(1)
        //         .generateAnomaly(1)
        //         .thresholdSeverity("High")
        //         .thresholdSeverityId(433)
        //         .build();
        // configList.add(config);
        KpiViolationConfig config = KpiViolationConfig.builder()
                .operation("not between")
                .minThreshold(30.0)
                .maxThreshold(40.0)
                .status(1)
                .generateAnomaly(1)
                .excludeMaintenance(1)
                .thresholdSeverity("Low")
                .thresholdSeverityId(431)
                .build();
        configList.add(config);
        config = KpiViolationConfig.builder()
                .operation("not between")
                .minThreshold(50.0)
                .maxThreshold(60.0)
                .status(1)
                .generateAnomaly(1)
                .excludeMaintenance(1)
                .thresholdSeverity("Medium")
                .thresholdSeverityId(432)
                .build();
        configList.add(config);
        config = KpiViolationConfig.builder()
                .operation("not between")
                .minThreshold(70.0)
                .maxThreshold(80.0)
                .status(1)
                .generateAnomaly(1)
                .excludeMaintenance(1)
                .thresholdSeverity("High")
                .thresholdSeverityId(433)
                .build();
        configList.add(config);
        Map<String, List<KpiViolationConfig>> violationProfiles = new HashMap<>();
        violationProfiles.put(attributeName, configList);

        CompInstKpiEntity compInstKpiEntity = new CompInstKpiEntity();
        compInstKpiEntity.setIsGroup(false);
        compInstKpiEntity.setIsInfo(1);
        compInstKpiEntity.setKpiViolationConfig(violationProfiles);

        KpiDetails kpiDetails = new KpiDetails();
        kpiDetails.setKpiViolationConfig(violationProfiles);

        cacheWrapperMock = Mockito.mock(CacheWrapper.class);
        Mockito.when(cacheWrapperMock.getInstanceKPIDetails(accountIdentifier, instanceIdentifier, kpiId))
                .thenReturn(compInstKpiEntity);
        process.cacheWrapper = cacheWrapperMock;
        Mockito.when(process.cacheWrapper.getInstanceKPIDetails(accountIdentifier, instanceIdentifier, kpiId))
                .thenReturn(compInstKpiEntity);

        Mockito.when(cacheWrapperMock.getServiceKPIDetails(accountIdentifier, instanceIdentifier, kpiId))
                .thenReturn(kpiDetails);
        process.cacheWrapper = cacheWrapperMock;
        Mockito.when(process.cacheWrapper.getServiceKPIDetails(accountIdentifier, instanceIdentifier, kpiId))
                .thenReturn(kpiDetails);

        MetaData metaData = new MetaData();
        metaData.setViolationLevel("INSTANCE");
        metaData.setServiceIdentifier("svc1");

        // Use reflection to invoke the private violatedKpi method
        Method method = ViolatedEventsProcess.class.getDeclaredMethod("violatedKpi",
                AggregatedKpiProtos.AggregatedKpi.class, String.class, String.class, Map.class, MetaData.class);
        method.setAccessible(true);
        @SuppressWarnings("unchecked")
        List<ViolatedData> result = (List<ViolatedData>) method.invoke(process, aggregatedKpi, attributeName, value, violationProfiles, metaData);

        // Expecting a non-null & non-empty list when a valid config exists
        assertNotNull(result, "Result should not be null for valid violation config.");
        assertFalse(result.isEmpty(), "Result should contain at least one ViolatedData.");
        assertEquals(2, result.size());
    }

    @Test
    void testViolatedKpi_noConfig() throws Exception {
        ViolatedEventsProcess process = new ViolatedEventsProcess();

        String value = "65";

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = Mockito.mock(AggregatedKpiProtos.AggregatedKpi.class);
        KPIAgentMessageProtos.KPIAgentMessage.KpiData kpiData = Mockito.mock(KPIAgentMessageProtos.KPIAgentMessage.KpiData.class);
        Mockito.when(aggregatedKpi.getKpiData()).thenReturn(kpiData);
        Mockito.when(kpiData.getKpiUid()).thenReturn(1);
        Mockito.when(kpiData.getVal()).thenReturn(value);

        // Create an empty violation configuration list, so the method returns null
        Map<String, List<KpiViolationConfig>> violationProfiles = new HashMap<>();
        violationProfiles.put("ALL", new ArrayList<>());
        MetaData metaData = new MetaData();

        // Use reflection to invoke the private violatedKpi method
        Method method = ViolatedEventsProcess.class.getDeclaredMethod("violatedKpi",
                AggregatedKpiProtos.AggregatedKpi.class, String.class, String.class, Map.class, MetaData.class);
        method.setAccessible(true);
        @SuppressWarnings("unchecked")
        List<ViolatedData> result = (List<ViolatedData>) method.invoke(process, aggregatedKpi, "ALL", value, violationProfiles, metaData);

        // Expecting a null result when there is no valid violation configuration
        assertNull(result, "Result should be null when violation config is unavailable.");
    }

    @Test
    void testCreateViolatedDataObject_validInput() throws Exception {
        ViolatedEventsProcess process = new ViolatedEventsProcess();
        // ViolatedEventsProcess process = Mockito.spy(new ViolatedEventsProcess());
        violatedEventsProcess.cacheWrapper = cacheWrapperMock;
        String accountIdentifier =  "demo";
        int kpiId = 1;
        String instanceIdentifier = "1fa5190c-ce1f-4da0-9f72-51e7d70f73ca";
        String attributeName = "ALL";
        // ProtocolStringList mockThresholdMap = Mockito.mock(ProtocolStringList.class);
        // Mockito.when(mockAppIdList.get(0)).thenReturn("app1");
        // Mockito.when(mockAppIdList.size()).thenReturn(1);
        Map<String, Double> thresholdMap = new HashMap<>();
        thresholdMap.put("Lower", 10.0);
        thresholdMap.put("Upper", 100.0);
        String operation = "greater than";

        // Create a non-empty violation configuration map for attribute "ALL"
        List<KpiViolationConfig> configList = new ArrayList<>();
        KpiViolationConfig config = KpiViolationConfig.builder()
                .operation("not between")
                .minThreshold(30.0)
                .maxThreshold(40.0)
                .status(1)
                .generateAnomaly(1)
                .thresholdSeverity("Low")
                .thresholdSeverityId(431)
                .excludeMaintenance(1)
                .generateAnomaly(1)
                .build();
        configList.add(config);
        Map<String, List<KpiViolationConfig>> violationProfiles = new HashMap<>();
        violationProfiles.put(attributeName, configList);

        CompInstKpiEntity compInstKpiEntity = new CompInstKpiEntity();
        compInstKpiEntity.setIsGroup(false);
        compInstKpiEntity.setIsInfo(1);
        compInstKpiEntity.setKpiViolationConfig(violationProfiles);

        KpiDetails kpiDetails = new KpiDetails();
        kpiDetails.setKpiViolationConfig(violationProfiles);

        cacheWrapperMock = Mockito.mock(CacheWrapper.class);
        Mockito.when(cacheWrapperMock.getInstanceKPIDetails(accountIdentifier, instanceIdentifier, kpiId))
                .thenReturn(compInstKpiEntity);
        process.cacheWrapper = cacheWrapperMock;
        Mockito.when(process.cacheWrapper.getInstanceKPIDetails(accountIdentifier, instanceIdentifier, kpiId))
                .thenReturn(compInstKpiEntity);

        Mockito.when(cacheWrapperMock.getServiceKPIDetails(accountIdentifier, instanceIdentifier, kpiId))
                .thenReturn(kpiDetails);
        process.cacheWrapper = cacheWrapperMock;
        Mockito.when(process.cacheWrapper.getServiceKPIDetails(accountIdentifier, instanceIdentifier, kpiId))
                .thenReturn(kpiDetails);

        String value = "65";

        ProtocolStringList mockAppIdList = Mockito.mock(ProtocolStringList.class);
        Mockito.when(mockAppIdList.get(0)).thenReturn("app1");
        Mockito.when(mockAppIdList.size()).thenReturn(1);
        ProtocolStringList serviceIdList = Mockito.mock(ProtocolStringList.class);
        Mockito.when(serviceIdList.get(0)).thenReturn("svc1");
        Mockito.when(serviceIdList.size()).thenReturn(1);

        // Mock AggregatedKpiProtos.AggregatedKpi and its KpiData
        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = Mockito.mock(AggregatedKpiProtos.AggregatedKpi.class);
        KPIAgentMessageProtos.KPIAgentMessage.KpiData kpiData = Mockito.mock(KPIAgentMessageProtos.KPIAgentMessage.KpiData.class);
        Mockito.when(aggregatedKpi.getKpiData()).thenReturn(kpiData);
        Mockito.when(aggregatedKpi.getAccountId()).thenReturn(accountIdentifier);
        Mockito.when(aggregatedKpi.getInstanceId()).thenReturn(instanceIdentifier);
        Mockito.when(aggregatedKpi.getApplicationIdList()).thenReturn(mockAppIdList);
        Mockito.when(aggregatedKpi.getServiceIdList()).thenReturn(serviceIdList);
        Mockito.when(aggregatedKpi.getTimeZoneOffsetInSec()).thenReturn(0);
        Mockito.when(aggregatedKpi.getMetaDataMap()).thenReturn(new HashMap<>());
        Mockito.when(kpiData.getKpiUid()).thenReturn(kpiId);
        Mockito.when(kpiData.getKpiType()).thenReturn(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);
        Mockito.when(kpiData.getIsKpiGroup()).thenReturn(false);
        Mockito.when(kpiData.getTimeInGMT()).thenReturn("2025-05-21 10:00:00");

        // Mock MetaData
        MetaData metaData = MetaData.builder().violationLevel("INSTANCE").build();

        // Mock OperationType
        OperationType opType = OperationType.GREATER_THAN;

        // Thresholds
        Map<String, Double> thresholds = new HashMap<>();
        thresholds.put("Lower", 10.0);
        thresholds.put("Upper", 100.0);

        // Use reflection to invoke the private method
        Method method = ViolatedEventsProcess.class.getDeclaredMethod(
                "createViolatedDataObject",
                AggregatedKpiProtos.AggregatedKpi.class,
                OperationType.class,
                String.class,
                String.class,
                String.class,
                Map.class,
                MetaData.class
        );
        method.setAccessible(true);

        ViolatedData result = (ViolatedData) method.invoke(
                process,
                aggregatedKpi,
                opType,
                "431",
                attributeName,
                value,
                thresholds,
                metaData
        );

        assertNotNull(result, "ViolatedData should not be null for valid input.");
        assertEquals(accountIdentifier, result.getAccountId());
        assertEquals(instanceIdentifier, result.getInstanceId());
        assertEquals("431", result.getThresholdSeverity());
        assertEquals(attributeName, result.getKpiAttribute());
        assertEquals(value, result.getValue());
    }
}