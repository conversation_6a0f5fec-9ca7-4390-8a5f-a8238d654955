package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.AggregatedKpiProtos;
import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.heal.event.detector.pojos.ViolatedData;
import com.heal.event.detector.util.ProtoCreator;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Spy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class ViolatedEventsProcessIT {
    @Spy
    ViolatedEventsProcess violatedEventsProcess;
    @Autowired
    ProtoCreator protoCreator;
    @Autowired
    GetViolatedData getViolatedData;

    @BeforeEach
    public void initialize() {
        violatedEventsProcess.getViolatedData = getViolatedData;
        violatedEventsProcess.outOfOrderValue = 5;
        violatedEventsProcess.lowSeveritySignal = "Severe";
        violatedEventsProcess.highSeveritySignal = "Default";
    }

    @Test
    public void processAggregatedKpiData_invalidAccountId() {

        String accountIdentifier = "Sample_Acc_" + RandomStringUtils.random(16, true, true);
        String instanceIdentifier = "RHEL_NB_App_Host_146_Inst_1-DR";
        int kpiId = 24;

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator
                .createAggregatedKPIBodyNonGrpKpiType(accountIdentifier,
                        "netbanking_1_DR", "NB-App-Service-DR", instanceIdentifier, kpiId,
                        "Packets Sent", 60, 19800, "50",
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        List<ViolatedData> violatedData = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
        assert violatedData.isEmpty();

    }

    @Test
    public void processAggregatedKpiData_invalidInstanceId() {

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String instanceIdentifier = "Sample_Inst_" + RandomStringUtils.random(16, true, true);
        int kpiId = 24;

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator
                .createAggregatedKPIBodyNonGrpKpiType(accountIdentifier,
                        "netbanking_1_DR", "NB-App-Service-DR", instanceIdentifier, kpiId,
                        "Packets Sent", 60, 19800, "50",
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        List<ViolatedData> violatedData = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
        assert violatedData.isEmpty();

    }

    @Test
    public void processAggregatedKpiData_invalidKPIId() {

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String instanceIdentifier = "RHEL_NB_App_Host_146_Inst_1-DR";
        int kpiId = *********;
        String serviceIdentifier = "NB-App-Service-DR";

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator
                .createAggregatedKPIBodyNonGrpKpiType(accountIdentifier,
                        "netbanking_1_DR", serviceIdentifier, instanceIdentifier, kpiId,
                        "Packets Sent", 60, 19800, "50",
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        List<ViolatedData> violatedData = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
        assert violatedData.isEmpty();

    }

    @Test
    public void processAggregatedKpiData_validCase_nonGrpKpi_WithInstanceKpiConfigMapNonNull() {

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String instanceIdentifier = "SOLARIS_ENET_HOST_112_Inst_1-DC";
        int kpiId = 8;
        String serviceIdentifier = "ENET-App-Service-DC";

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator
                .createAggregatedKPIBodyNonGrpKpiType(accountIdentifier,
                        "enet_3_DC", serviceIdentifier, instanceIdentifier, kpiId,
                        "CPU Busy", 60, 19800, "800000",
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        List<ViolatedData> violatedData = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
        assert violatedData != null;
        assert !violatedData.isEmpty();

    }

    @Test
    public void processAggregatedKpiData_validCase_nonGrpKpi_WithBothInstanceServiceKpiConfigMapNonNull() {

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String instanceIdentifier = "SOLARIS_ENET_HOST_112_Inst_1-DR";
        int kpiId = 8;
        String serviceIdentifier = "ENET-App-Service-DR";

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator
                .createAggregatedKPIBodyNonGrpKpiType(accountIdentifier,
                        "enet_3_DR", serviceIdentifier, instanceIdentifier, kpiId,
                        "CPU Busy", 60, 19800, "80",
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        List<ViolatedData> violatedData = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
        assert violatedData != null;
        assert !violatedData.isEmpty();

    }

    @Test
    public void processAggregatedKpiData_validCase_nonGrpKpi_WithInstanceKpiConfigMapNull_ServiceKpiConfigMapNonNull() {

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String instanceIdentifier = "WINDOWS_54_Host-DC";
        int kpiId = 1;
        String serviceIdentifier = "IIS-Web-Service-DC";

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator
                .createAggregatedKPIBodyNonGrpKpiType(accountIdentifier,
                        "microbanking_1_DC", serviceIdentifier, instanceIdentifier, kpiId,
                        "CPU Util", 60, 19800, "80",
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        List<ViolatedData> violatedData = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
        assert violatedData != null;
        assert !violatedData.isEmpty();

    }

    @Test
    public void processAggregatedKpiData_validCase_nonGrpKpi_WithInstanceKpiConfigMapNull_ServiceKpiConfigMapNonNull_MultipleServices() {

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String instanceIdentifier = "WINDOWS_54_Host-DC";
        int kpiId = 1;
        String serviceIdentifier = "IIS-Web-Service-DC,MSSQL-DB-Service-DC";

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator
                .createAggregatedKPIBodyNonGrpKpiType(accountIdentifier,
                        "microbanking_1_DC", serviceIdentifier, instanceIdentifier, kpiId,
                        "CPU Util", 60, 19800, "80",
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        List<ViolatedData> violatedData = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
        assert violatedData != null;
        assert !violatedData.isEmpty();

    }

    @Test
    public void processAggregatedKpiData_validCase_nonGrpKpi_WithBothInstanceServiceKpiConfigMapNull() {

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String instanceIdentifier = "WINDOWS_54_Host-DR";
        int kpiId = 1;
        String serviceIdentifier = "IIS-Web-Service-DR";

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator
                .createAggregatedKPIBodyNonGrpKpiType(accountIdentifier,
                        "netbanking_1_DR", serviceIdentifier, instanceIdentifier, kpiId,
                        "CPU Util", 60, 19800, "800",
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        List<ViolatedData> violatedData = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
        assert violatedData.isEmpty();

    }


    @Test
    public void processAggregatedKpiData_invalidCase_ConfigWatchKPI_WithInstanceKpiConfigMapNonNull() {

        Map<String, String> grpKpiMap = new HashMap<>();

        Map<String, KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi> watcherKpiMap = new HashMap<>();
        watcherKpiMap.put("ALL", KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.newBuilder().putAllPairs(grpKpiMap).build());

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String instanceIdentifier = "SOLARIS_ENET_HOST_112_Inst_1-DC";
        int kpiId = 259;
        String serviceIdentifier = "ENET-App-Service-DC";

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator
                .createAggregatedKPIBodyWatcherKpiType(accountIdentifier,
                        "enet_3_DC", serviceIdentifier, instanceIdentifier, kpiId,
                        "System Properties", 60, true, 19800, watcherKpiMap,
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.ConfigWatch);

        List<ViolatedData> violatedData = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
        assert violatedData.isEmpty();

    }

    @Test
    public void processAggregatedKpiData_validCase_ConfigWatchKPI_WithInstanceKpiConfigMapNonNull() {

        Map<String, String> grpKpiMap = new HashMap<>();
        grpKpiMap.put("ALL", "100");

        Map<String, KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi> watcherKpiMap = new HashMap<>();
        watcherKpiMap.put("ALL", KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.newBuilder().putAllPairs(grpKpiMap).build());

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String instanceIdentifier = "SOLARIS_ENET_HOST_112_Inst_1-DC";
        int kpiId = 259;
        String serviceIdentifier = "ENET-App-Service-DC";

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator
                .createAggregatedKPIBodyWatcherKpiType(accountIdentifier,
                        "enet_3_DC", serviceIdentifier, instanceIdentifier, kpiId,
                        "System Properties", 60, true, 19800, watcherKpiMap,
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.ConfigWatch);

        List<ViolatedData> violatedData = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
        assert violatedData != null;

    }

    @Test
    public void processAggregatedKpiData_validCase_ConfigWatchKPI_WithBothInstanceServiceKpiConfigMapNonNull() {

        Map<String, String> grpKpiMap = new HashMap<>();
        grpKpiMap.put("ALL", "200");

        Map<String, KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi> watcherKpiMap = new HashMap<>();
        watcherKpiMap.put("ALL", KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.newBuilder().putAllPairs(grpKpiMap).build());

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String instanceIdentifier = "SOLARIS_ENET_HOST_112_Inst_1-DR";
        int kpiId = 259;
        String serviceIdentifier = "ENET-App-Service-DR";

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator
                .createAggregatedKPIBodyWatcherKpiType(accountIdentifier,
                        "enet_3_DR", serviceIdentifier, instanceIdentifier, kpiId,
                        "System Properties", 60, true, 19800, watcherKpiMap,
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.ConfigWatch);

        List<ViolatedData> violatedData = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
        assert violatedData != null;

    }


    @Test
    public void processAggregatedKpiData_validCase_FileWatchKPI_WithInstanceKpiConfigMapNonNull() {

        Map<String, String> grpKpiMap = new HashMap<>();
        grpKpiMap.put("ALL", "2000");
        grpKpiMap.put("/data/RAGHAV/ConfigDataDC/fsconf.properties", "2000");
        grpKpiMap.put("/etc/crontab", "2000");

        Map<String, KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi> watcherKpiMap = new HashMap<>();
        watcherKpiMap.put("ALL", KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.newBuilder().putAllPairs(grpKpiMap).build());

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String instanceIdentifier = "SOLARIS_ENET_HOST_112_Inst_1-DC";
        int kpiId = 265;
        String serviceIdentifier = "ENET-App-Service-DC";

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator
                .createAggregatedKPIBodyWatcherKpiType(accountIdentifier,
                        "enet_3_DC", serviceIdentifier, instanceIdentifier, kpiId,
                        "File Watch", 60, true, 19800, watcherKpiMap,
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.FileWatch);

        List<ViolatedData> violatedData = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
        assert violatedData != null;

    }

    @Test
    public void processAggregatedKpiData_validCase_FileWatchKPI_WithBothInstanceServiceKpiConfigMapNonNull() {

        Map<String, String> grpKpiMap = new HashMap<>();
        grpKpiMap.put("ALL", "200");
        grpKpiMap.put("/data/RAGHAV/ConfigDataDR/hcconf.properties", "200");

        Map<String, KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi> watcherKpiMap = new HashMap<>();
        watcherKpiMap.put("/etc/conf.props", KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.newBuilder().putAllPairs(grpKpiMap).build());

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String instanceIdentifier = "SOLARIS_ENET_HOST_112_Inst_1-DR";
        int kpiId = 265;
        String serviceIdentifier = "ENET-App-Service-DR";

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator
                .createAggregatedKPIBodyWatcherKpiType(accountIdentifier,
                        "enet_3_DR", serviceIdentifier, instanceIdentifier, kpiId,
                        "File Watch", 60, true, 19800, watcherKpiMap,
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.FileWatch);

        List<ViolatedData> violatedData = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
        assert violatedData != null;

    }


    @Test
    public void processAggregatedKpiData_validCase_GrpKpi_WithInstanceKpiConfigMapNonNull() {

        Map<String, String> grpKpiMap = new HashMap<>();
        grpKpiMap.put("ALL", "1500");
        grpKpiMap.put("Sample", "1500");

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String instanceIdentifier = "SOLARIS_ENET_HOST_112_Inst_1-DC";
        int kpiId = 18;
        String serviceIdentifier = "ENET-App-Service-DC";

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator
                .createAggregatedKPIBodyGrpKpiType(accountIdentifier,
                        "enet_3_DC", serviceIdentifier, instanceIdentifier, kpiId,
                        "Available Size Host (MB)", 60, 19800, grpKpiMap,
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        List<ViolatedData> violatedData = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
        assert violatedData != null;

    }

    @Test
    public void processAggregatedKpiData_validCase_GrpKpi_WithBothInstanceServiceKpiConfigMapNonNull() {

        Map<String, String> grpKpiMap = new HashMap<>();
        grpKpiMap.put("ALL", "1500");

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String instanceIdentifier = "SOLARIS_ENET_HOST_112_Inst_1-DR";
        int kpiId = 8;
        String serviceIdentifier = "ENET-App-Service-DR";

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator
                .createAggregatedKPIBodyGrpKpiType(accountIdentifier,
                        "enet_3_DR", serviceIdentifier, instanceIdentifier, kpiId,
                        "Available Size Host (MB)", 60, 19800, grpKpiMap,
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        List<ViolatedData> violatedData = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
        assert violatedData != null;

    }

    @Test
    public void processAggregatedKpiData_validCase_GrpKpi_WithBothInstanceServiceKpiConfigMapNonNull_ForDifferentAttributes() {

        Map<String, String> grpKpiMap = new HashMap<>();
        grpKpiMap.put("InstanceLevel", "70");
        grpKpiMap.put("ServiceLevel", "70");

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String instanceIdentifier = "SOLARIS_ENET_HOST_112_Inst_1-DR";
        int kpiId = 53;
        String serviceIdentifier = "ENET-App-Service-DR";

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator
                .createAggregatedKPIBodyGrpKpiType(accountIdentifier,
                        "enet_3_DR", serviceIdentifier, instanceIdentifier, kpiId,
                        "Cap Value", 60, 19800, grpKpiMap,
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        List<ViolatedData> violatedData = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
        assert violatedData != null;

    }

    @Test
    public void processAggregatedKpiData_validCase_GrpKpi_WithInstanceKpiConfigMapNull_ServiceKpiConfigMapNonNull() {

        Map<String, String> grpKpiMap = new HashMap<>();
        grpKpiMap.put("ALL", "80.0");

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String instanceIdentifier = "WINDOWS_54_Host-DC";
        int kpiId = 18;
        String serviceIdentifier = "IIS-Web-Service-DC";

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator
                .createAggregatedKPIBodyGrpKpiType(accountIdentifier,
                        "microbanking_1_DC", serviceIdentifier, instanceIdentifier, kpiId,
                        "Available Size Host (MB)", 60, 19800, grpKpiMap,
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);


        List<ViolatedData> violatedData = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
        assert violatedData != null;

    }

    @Test
    public void processAggregatedKpiData_validCase_GrpKpi_WithInstanceKpiConfigMapNull_ServiceKpiConfigMapNonNull_MultipleServices() {

        Map<String, String> grpKpiMap = new HashMap<>();
        grpKpiMap.put("ALL", "80.0");

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String instanceIdentifier = "WINDOWS_54_Host-DC";
        int kpiId = 18;
        String serviceIdentifier = "IIS-Web-Service-DC,MSSQL-DB-Service-DC";

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator
                .createAggregatedKPIBodyGrpKpiType(accountIdentifier,
                        "microbanking_1_DC", serviceIdentifier, instanceIdentifier, kpiId,
                        "Available Size Host (MB)", 60, 19800, grpKpiMap,
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);


        List<ViolatedData> violatedData = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
        assert violatedData != null;

    }

    @Test
    public void processAggregatedKpiData_validCase_GrpKpi_WithBothInstanceServiceKpiConfigMapNull() {

        Map<String, String> grpKpiMap = new HashMap<>();
        grpKpiMap.put("ALL", "80.0");

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String instanceIdentifier = "WINDOWS_54_Host-DR";
        int kpiId = 18;
        String serviceIdentifier = "IIS-Web-Service-DR";

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator
                .createAggregatedKPIBodyGrpKpiType(accountIdentifier,
                        "netbanking_1_DR", serviceIdentifier, instanceIdentifier, kpiId,
                        "Available Size Host (MB)", 60, 19800, grpKpiMap,
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);


        List<ViolatedData> violatedData = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
        assert violatedData.isEmpty();

    }

    /*
    Run this test case after setting maintenance window for the instance
     */
//    @Test
//    public void processViolatedKpiData_kpiInfoType_nonGrp_instanceUnderMaintenance() {
//
//        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
//        int kpiId = 180;
//        String instanceIdentifier = "ORACLE_ENET_DB_112_Inst_1-DC";
//        String serviceIdentifier = "ENET-DB-Service-DC";
//        String applicationIdentifier = "enet_3_DC";
//
//        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator
//                .createAggregatedKPIBodyNonGrpKpiType(accountIdentifier,
//                        applicationIdentifier, serviceIdentifier, instanceIdentifier, kpiId,
//                        "CPU Usage", 60, 19800, "80",
//                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);
//
//        List<ViolatedData> violatedData = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
//        assert violatedData.isEmpty();
//
//    }

    /*
    Run this test case after setting maintenance window for the service
     */
//    @Test
//    public void processViolatedKpiData_kpiInfoType_nonGrp_serviceUnderMaintenance() {
//
//        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
//        int kpiId = 180;
//        String instanceIdentifier = "ORACLE_ENET_DB_112_Inst_1-DR";
//        String serviceIdentifier = "ENET-DB-Service-DR";
//        String applicationIdentifier = "enet_3_DR";
//
//        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator
//                .createAggregatedKPIBodyNonGrpKpiType(accountIdentifier,
//                        applicationIdentifier, serviceIdentifier, instanceIdentifier, kpiId,
//                        "CPU Usage", 60, 19800, "80",
//                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);
//
//        List<ViolatedData> violatedData = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
//        assert violatedData.isEmpty();
//
//    }
}