package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.appnomic.appsone.common.protbuf.ViolatedEventProtos;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.CompInstKpiEntity;
import com.heal.configuration.pojos.KpiDetails;
import com.heal.configuration.pojos.KpiViolationConfig;
import com.heal.event.detector.pojos.ViolatedData;
import com.heal.event.detector.pojos.enums.ViolationEventType;
import com.heal.event.detector.repo.RedisUtilities;
import com.heal.event.detector.util.ProtoCreator;
import com.heal.event.detector.utility.Constants;
import com.heal.event.detector.utility.cache.CacheWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
// import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
// import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Method;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
// @ExtendWith(MockitoExtension.class)
public class GetViolatedDataTest {

    @Spy
    private GetViolatedData getViolatedData;

    @Autowired
    ProtoCreator protoCreator;

    @Mock
    RedisUtilities redisUtilitiesMock;

    @Mock
    CacheWrapper cacheWrapperMock;

    @BeforeEach
    void setUp() {
        getViolatedData = new GetViolatedData();
    }

    public CompInstKpiEntity getInstanceDetails(int instanceId, String attributeName, int kpiId, Map<String, Double> thresholdMap,
                                                String operation, int severity, int generateAnomaly, int excludeMaintenance) {
        List<KpiViolationConfig> kpiViolationConfigList = new ArrayList<>();
        kpiViolationConfigList.add(KpiViolationConfig.builder()
                .operation(operation)
                .minThreshold(thresholdMap.get("Lower"))
                .maxThreshold(thresholdMap.get("Upper"))
                .severity(severity)
                .generateAnomaly(generateAnomaly)
                .excludeMaintenance(excludeMaintenance)
                .kpiId(kpiId)
                .compInstanceId(instanceId)
                .build());
        Map<String, List<KpiViolationConfig>> kpiViolationConfigMap = new HashMap<>();
        kpiViolationConfigMap.put(attributeName, kpiViolationConfigList);

        return CompInstKpiEntity.builder()
                .kpiViolationConfig(kpiViolationConfigMap)
                .build();
    }

    public CompInstClusterDetails getCompInstClusterDetails(int instanceId) {
        return CompInstClusterDetails.builder()
                .id(instanceId)
                .build();
    }

    private KpiDetails getServiceKpiDetails(int compInstanceId, int kpiId, Map<String, Double> thresholdMap,
                                            String operation, int severity, int generateAnomaly, int excludeMaintenance, String attribute) {
        List<KpiViolationConfig> kpiViolationConfigList = new ArrayList<>();
        kpiViolationConfigList.add(KpiViolationConfig.builder()
                .operation(operation)
                .minThreshold(thresholdMap.get("Lower"))
                .maxThreshold(thresholdMap.get("Upper"))
                .severity(severity)
                .generateAnomaly(generateAnomaly)
                .excludeMaintenance(excludeMaintenance)
                .kpiId(kpiId)
                .applicableTo("clusters")
                .definedBy("USER")
                .compInstanceId(compInstanceId)
                .attributeValue(attribute)
                .build());
        Map<String, List<KpiViolationConfig>> kpiViolationConfigMap = new HashMap<>();
        kpiViolationConfigMap.put("ALL", kpiViolationConfigList);

        return KpiDetails.builder()
                .kpiViolationConfig(kpiViolationConfigMap)
                .build();

    }

    @Test
    void validateGenerateAnomalyMethodStaticThresholdType_genAnomalyFlagTrue() {

        String accountIdentifier = "heal_health";
        String kpiId = "16";
        String instanceIdentifier = "29a771b7-3c01-4cca-a903-d7eb78aba8f5";
        int instanceId = 9;
        String attributeName = "ALL";
        Map<String, Double> thresholdMap = protoCreator.thresholdMapProvider();
        String operation = "greater than";
        String severity = "Severe";

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountIdentifier,
                        "Heal_Health_App", "service123", instanceIdentifier,
                        kpiId, attributeName,
                        "60.0", 19800, "Static",
                        operation, severity, thresholdMap);


        CompInstKpiEntity instanceKpiDetails = getInstanceDetails(instanceId, attributeName, Integer.parseInt(kpiId),
                thresholdMap, operation, 1, 1, 0);

        assert getViolatedData.getGenerateAnomalyStatus(violatedEvent, instanceKpiDetails, violatedEvent.getKpis(0));

    }

    @Test
    void validateGenerateAnomalyMethodStaticThresholdType_genAnomalyFlagFalse() {

        String accountIdentifier = "heal_health";
        String kpiId = "16";
        String instanceIdentifier = "29a771b7-3c01-4cca-a903-d7eb78aba8f5";
        int instanceId = 9;
        String attributeName = "ALL";
        Map<String, Double> thresholdMap = protoCreator.thresholdMapProvider();
        String operation = "greater than";
        String severity = "Severe";

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountIdentifier,
                        "Heal_Health_App", "service123", instanceIdentifier,
                        kpiId, attributeName,
                        "60.0", 19800, "Static",
                        operation, severity, thresholdMap);


        CompInstKpiEntity instanceKpiDetails = getInstanceDetails(instanceId, attributeName, Integer.parseInt(kpiId),
                thresholdMap, operation, 1, 0, 0);

        assert !getViolatedData.getGenerateAnomalyStatus(violatedEvent, instanceKpiDetails, violatedEvent.getKpis(0));

    }

    @Test
    void validateGenerateAnomalyMethodRealtimeThresholdType_genAnomalyFlagTrue() {

        String accountIdentifier = "heal_health";
        String kpiId = "16";
        String instanceIdentifier = "29a771b7-3c01-4cca-a903-d7eb78aba8f5";
        int instanceId = 9;
        String attributeName = "ALL";
        Map<String, Double> thresholdMap = protoCreator.thresholdMapProvider();
        String operation = "greater than";
        String severity = "Severe";

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountIdentifier,
                        "Heal_Health_App", "service123", instanceIdentifier,
                        kpiId, attributeName,
                        "60.0", 19800, "RealTime",
                        operation, severity, thresholdMap);


        CompInstKpiEntity instanceKpiDetails = getInstanceDetails(instanceId, attributeName, Integer.parseInt(kpiId),
                thresholdMap, operation, 1, 1, 0);

        assert getViolatedData.getGenerateAnomalyStatus(violatedEvent, instanceKpiDetails, violatedEvent.getKpis(0));

    }

    @Test
    void validateGenerateAnomalyMethodRealtimeThresholdType_genAnomalyFlagFalse() {

        String accountIdentifier = "heal_health";
        String kpiId = "16";
        String instanceIdentifier = "29a771b7-3c01-4cca-a903-d7eb78aba8f5";
        int instanceId = 9;
        String attributeName = "ALL";
        Map<String, Double> thresholdMap = protoCreator.thresholdMapProvider();
        String operation = "greater than";
        String severity = "Severe";

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountIdentifier,
                        "Heal_Health_App", "service123", instanceIdentifier,
                        kpiId, attributeName,
                        "60.0", 19800, "RealTime",
                        operation, severity, thresholdMap);


        CompInstKpiEntity instanceKpiDetails = getInstanceDetails(instanceId, attributeName, Integer.parseInt(kpiId),
                thresholdMap, operation, 1, 0, 0);

        assert getViolatedData.getGenerateAnomalyStatus(violatedEvent, instanceKpiDetails, violatedEvent.getKpis(0));

    }

    @Test
    void validateGenerateAnomalyMethodStaticThresholdType_instanceViolationConfigNull_serviceViolationNotNull_genAnomalyTrue() {

        getViolatedData.redisUtilities = redisUtilitiesMock;

        String accountIdentifier = "heal_health";
        String kpiId = "16";
        String instanceIdentifier = "29a771b7-3c01-4cca-a903-d7eb78aba8f5";
        int instanceId = 9;
        String attributeName = "ALL";
        Map<String, Double> thresholdMap = protoCreator.thresholdMapProvider();
        String operation = "greater than";
        String severity = "Severe";
        String serviceIdentifier = "service123";

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountIdentifier,
                        "Heal_Health_App", serviceIdentifier, instanceIdentifier,
                        kpiId, attributeName,
                        "60.0", 19800, "Static",
                        operation, severity, thresholdMap);


        CompInstKpiEntity instanceKpiDetails = CompInstKpiEntity.builder().kpiViolationConfig(null).build();

        getViolatedData.redisUtilities = redisUtilitiesMock;
        Mockito.when(getViolatedData.redisUtilities.getServiceKPIDetails(accountIdentifier, serviceIdentifier, Integer.parseInt(kpiId)))
                .thenReturn(getServiceKpiDetails(instanceId, Integer.parseInt(kpiId),
                        thresholdMap, operation, 1, 1, 0, "ALL"));

        assert getViolatedData.getGenerateAnomalyStatus(violatedEvent, instanceKpiDetails, violatedEvent.getKpis(0));

    }

    @Test
    void validateGenerateAnomalyMethodStaticThresholdType_instanceViolationConfigNull_serviceViolationNotNull_genAnomalyFalse() {

        getViolatedData.redisUtilities = redisUtilitiesMock;

        String accountIdentifier = "heal_health";
        String kpiId = "16";
        String instanceIdentifier = "29a771b7-3c01-4cca-a903-d7eb78aba8f5";
        int instanceId = 9;
        String attributeName = "ALL";
        Map<String, Double> thresholdMap = protoCreator.thresholdMapProvider();
        String operation = "greater than";
        String severity = "Severe";
        String serviceIdentifier = "service123";

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountIdentifier,
                        "Heal_Health_App", serviceIdentifier, instanceIdentifier,
                        kpiId, attributeName,
                        "60.0", 19800, "Static",
                        operation, severity, thresholdMap);


        CompInstKpiEntity instanceKpiDetails = CompInstKpiEntity.builder().kpiViolationConfig(null).build();

        getViolatedData.redisUtilities = redisUtilitiesMock;
        Mockito.when(getViolatedData.redisUtilities.getServiceKPIDetails(accountIdentifier, serviceIdentifier, Integer.parseInt(kpiId)))
                .thenReturn(getServiceKpiDetails(instanceId, Integer.parseInt(kpiId),
                        thresholdMap, operation, 1, 0, 0, "ALL"));

        assert !getViolatedData.getGenerateAnomalyStatus(violatedEvent, instanceKpiDetails, violatedEvent.getKpis(0));

    }

    @Test
    void validateGenerateAnomalyMethodStaticThresholdType_instanceViolationConfigNull_serviceViolationNotNull_DifferentAttribute_genAnomalyFalse() {

        getViolatedData.redisUtilities = redisUtilitiesMock;

        String accountIdentifier = "heal_health";
        String kpiId = "16";
        String instanceIdentifier = "29a771b7-3c01-4cca-a903-d7eb78aba8f5";
        int instanceId = 9;
        String attributeName = "ALL";
        Map<String, Double> thresholdMap = protoCreator.thresholdMapProvider();
        String operation = "greater than";
        String severity = "Severe";
        String serviceIdentifier = "service123";

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountIdentifier,
                        "Heal_Health_App", serviceIdentifier, instanceIdentifier,
                        kpiId, attributeName,
                        "60.0", 19800, "Static",
                        operation, severity, thresholdMap);


        CompInstKpiEntity instanceKpiDetails = CompInstKpiEntity.builder().kpiViolationConfig(null).build();

        getViolatedData.redisUtilities = redisUtilitiesMock;
        Mockito.when(getViolatedData.redisUtilities.getServiceKPIDetails(accountIdentifier, serviceIdentifier, Integer.parseInt(kpiId)))
                .thenReturn(getServiceKpiDetails(instanceId, Integer.parseInt(kpiId),
                        thresholdMap, operation, 1, 1, 0, "DUMMY"));

        assert !getViolatedData.getGenerateAnomalyStatus(violatedEvent, instanceKpiDetails, violatedEvent.getKpis(0));

    }

    @Test
    void validateGenerateAnomalyMethodRealTimeThresholdType_instanceViolationConfigNull_serviceViolationNotNull_genAnomalyTrue() {

        getViolatedData.redisUtilities = redisUtilitiesMock;

        String accountIdentifier = "heal_health";
        String kpiId = "16";
        String instanceIdentifier = "29a771b7-3c01-4cca-a903-d7eb78aba8f5";
        int instanceId = 9;
        String attributeName = "ALL";
        Map<String, Double> thresholdMap = protoCreator.thresholdMapProvider();
        String operation = "greater than";
        String severity = "Severe";
        String serviceIdentifier = "service123";

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountIdentifier,
                        "Heal_Health_App", serviceIdentifier, instanceIdentifier,
                        kpiId, attributeName,
                        "60.0", 19800, "RealTime",
                        operation, severity, thresholdMap);


        CompInstKpiEntity instanceKpiDetails = CompInstKpiEntity.builder().kpiViolationConfig(null).build();

        assert getViolatedData.getGenerateAnomalyStatus(violatedEvent, instanceKpiDetails, violatedEvent.getKpis(0));

    }

    @Test
    void validateGenerateAnomalyMethodRealTimeThresholdType_instanceViolationConfigNull_serviceViolationNotNull_genAnomalyFalse() {

        getViolatedData.redisUtilities = redisUtilitiesMock;

        String accountIdentifier = "heal_health";
        String kpiId = "16";
        String instanceIdentifier = "29a771b7-3c01-4cca-a903-d7eb78aba8f5";
        int instanceId = 9;
        String attributeName = "ALL";
        Map<String, Double> thresholdMap = protoCreator.thresholdMapProvider();
        String operation = "greater than";
        String severity = "Severe";
        String serviceIdentifier = "service123";

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountIdentifier,
                        "Heal_Health_App", serviceIdentifier, instanceIdentifier,
                        kpiId, attributeName,
                        "60.0", 19800, "RealTime",
                        operation, severity, thresholdMap);


        CompInstKpiEntity instanceKpiDetails = CompInstKpiEntity.builder().kpiViolationConfig(null).build();

        getViolatedData.redisUtilities = redisUtilitiesMock;
        Mockito.when(getViolatedData.redisUtilities.getServiceKPIDetails(accountIdentifier, serviceIdentifier, Integer.parseInt(kpiId)))
                .thenReturn(getServiceKpiDetails(instanceId, Integer.parseInt(kpiId),
                        thresholdMap, operation, 1, 0, 0, "ALL"));

        assert getViolatedData.getGenerateAnomalyStatus(violatedEvent, instanceKpiDetails, violatedEvent.getKpis(0));

    }

    @Test
    void validateGenerateAnomalyMethodStaticThresholdType_instanceViolationConfigNull_serviceViolationConfigMocked() {

        getViolatedData.redisUtilities = redisUtilitiesMock;

        String accountIdentifier = "heal_health";
        String kpiId = "16";
        String instanceIdentifier = "29a771b7-3c01-4cca-a903-d7eb78aba8f5";
        int instanceId = 9;
        String attributeName = "ALL";
        Map<String, Double> thresholdMap = protoCreator.thresholdMapProvider();
        String operation = "greater than";
        String severity = "Severe";

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountIdentifier,
                        "Heal_Health_App", "service123", instanceIdentifier,
                        kpiId, attributeName,
                        "60.0", 19800, "Static",
                        operation, severity, thresholdMap);


        CompInstKpiEntity instanceKpiDetails = CompInstKpiEntity.builder().kpiViolationConfig(null).build();

        assert !getViolatedData.getGenerateAnomalyStatus(violatedEvent, instanceKpiDetails, violatedEvent.getKpis(0));

    }

    @Test
    void validateGenerateAnomalyMethodRealtimeThresholdType_instanceViolationConfigNull_serviceViolationConfigMocked() {

        getViolatedData.redisUtilities = redisUtilitiesMock;

        String accountIdentifier = "heal_health";
        String kpiId = "16";
        String instanceIdentifier = "29a771b7-3c01-4cca-a903-d7eb78aba8f5";
        int instanceId = 9;
        String attributeName = "ALL";
        Map<String, Double> thresholdMap = protoCreator.thresholdMapProvider();
        String operation = "greater than";
        String severity = "Severe";

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountIdentifier,
                        "Heal_Health_App", "service123", instanceIdentifier,
                        kpiId, attributeName,
                        "60.0", 19800, "RealTime",
                        operation, severity, thresholdMap);


        CompInstKpiEntity instanceKpiDetails = CompInstKpiEntity.builder().kpiViolationConfig(null).build();

        assert getViolatedData.getGenerateAnomalyStatus(violatedEvent, instanceKpiDetails, violatedEvent.getKpis(0));

    }

    @Test
    void testCheckViolations_GetHighestSeverity_InstanceLevelConfig() throws Exception {
        GetViolatedData process = new GetViolatedData();
        String accountIdentifier =  "demo";
        int kpiId = 1;
        String instanceIdentifier = "1fa5190c-ce1f-4da0-9f72-51e7d70f73ca";

        ViolatedData violatedData = new ViolatedData(accountIdentifier, Arrays.asList("app1"));
        violatedData.setEventType(ViolationEventType.KPI_VIOLATION);
        violatedData.setKpiId(String.valueOf(kpiId));
        violatedData.setInstanceId(instanceIdentifier);
        violatedData.setKpiAttribute(Constants.COMMON_ATTRIBUTE);
        violatedData.setValue("55.0");
        violatedData.setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);
        Map<String, String> metaData = new HashMap<>();
        metaData.put("violationLevel", "INSTANCE");
        violatedData.setMetaData(metaData);

        // Mock instance config
        String attributeName = "ALL";
        List<KpiViolationConfig> configList = new ArrayList<>();
        KpiViolationConfig config = KpiViolationConfig.builder()
                .operation("not between")
                .minThreshold(30.0)
                .maxThreshold(40.0)
                .status(1)
                .generateAnomaly(1)
                .excludeMaintenance(1)
                .thresholdSeverity("Low")
                .thresholdSeverityId(431)
                .build();
        configList.add(config);
        config = KpiViolationConfig.builder()
                .operation("not between")
                .minThreshold(50.0)
                .maxThreshold(60.0)
                .status(1)
                .generateAnomaly(1)
                .excludeMaintenance(1)
                .thresholdSeverity("Medium")
                .thresholdSeverityId(432)
                .build();
        configList.add(config);
        config = KpiViolationConfig.builder()
                .operation("not between")
                .minThreshold(70.0)
                .maxThreshold(80.0)
                .status(1)
                .generateAnomaly(1)
                .excludeMaintenance(1)
                .thresholdSeverity("High")
                .thresholdSeverityId(433)
                .build();
        configList.add(config);
        Map<String, List<KpiViolationConfig>> violationProfiles = new HashMap<>();
        violationProfiles.put(attributeName, configList);

        CompInstKpiEntity compInstKpiEntity = new CompInstKpiEntity();
        compInstKpiEntity.setIsGroup(false);
        compInstKpiEntity.setIsInfo(1);
        compInstKpiEntity.setKpiViolationConfig(violationProfiles);

        KpiDetails kpiDetails = new KpiDetails();
        kpiDetails.setKpiViolationConfig(violationProfiles);

        cacheWrapperMock = Mockito.mock(CacheWrapper.class);
        Mockito.when(cacheWrapperMock.getInstanceKPIDetails(accountIdentifier, instanceIdentifier, kpiId))
                .thenReturn(compInstKpiEntity);
        process.cacheWrapper = cacheWrapperMock;
        Mockito.when(process.cacheWrapper.getInstanceKPIDetails(accountIdentifier, instanceIdentifier, kpiId))
                .thenReturn(compInstKpiEntity);

        Mockito.when(cacheWrapperMock.getServiceKPIDetails(accountIdentifier, instanceIdentifier, kpiId))
                .thenReturn(kpiDetails);
        process.cacheWrapper = cacheWrapperMock;
        Mockito.when(process.cacheWrapper.getServiceKPIDetails(accountIdentifier, instanceIdentifier, kpiId))
                .thenReturn(kpiDetails);

        Method method = GetViolatedData.class.getDeclaredMethod(
                "getViolatedDataListForAllViolations",
                ViolatedData.class);
        method.setAccessible(true);
        @SuppressWarnings("unchecked")
        List<ViolatedData> result = (List<ViolatedData>) method.invoke(process, violatedData);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("431", result.get(0).getThresholdSeverity());
        assertEquals("433", result.get(1).getThresholdSeverity());
    }

    @Test
    void testCheckViolations_GetHighestSeverity_ServiceLevelConfig() throws Exception {
        GetViolatedData process = new GetViolatedData();
        String accountIdentifier =  "demo";
        int kpiId = 1;
        String instanceIdentifier = "1fa5190c-ce1f-4da0-9f72-51e7d70f73ca";

        ViolatedData violatedData = new ViolatedData(accountIdentifier, Arrays.asList("app1"));
        violatedData.setEventType(ViolationEventType.KPI_VIOLATION);
        violatedData.setKpiId(String.valueOf(kpiId));
        violatedData.setInstanceId(instanceIdentifier);
        violatedData.setKpiAttribute(Constants.COMMON_ATTRIBUTE);
        violatedData.setValue("55.0");
        violatedData.setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);
        Map<String, String> metaData = new HashMap<>();
        metaData.put("violationLevel", "SERVICE");
        violatedData.setMetaData(metaData);

        // Mock service config
        String attributeName = "/home";
        List<KpiViolationConfig> configList = new ArrayList<>();
        KpiViolationConfig config = KpiViolationConfig.builder()
                .operation("not between")
                .minThreshold(30.0)
                .maxThreshold(40.0)
                .status(1)
                .generateAnomaly(1)
                .excludeMaintenance(1)
                .thresholdSeverity("Low")
                .thresholdSeverityId(431)
                .build();
        configList.add(config);
        config = KpiViolationConfig.builder()
                .operation("not between")
                .minThreshold(50.0)
                .maxThreshold(60.0)
                .status(1)
                .generateAnomaly(1)
                .excludeMaintenance(1)
                .thresholdSeverity("Medium")
                .thresholdSeverityId(432)
                .build();
        configList.add(config);
        config = KpiViolationConfig.builder()
                .operation("not between")
                .minThreshold(70.0)
                .maxThreshold(80.0)
                .status(1)
                .generateAnomaly(1)
                .excludeMaintenance(1)
                .thresholdSeverity("High")
                .thresholdSeverityId(433)
                .build();
        configList.add(config);
        Map<String, List<KpiViolationConfig>> violationProfiles = new HashMap<>();
        violationProfiles.put(attributeName, configList);

        CompInstKpiEntity compInstKpiEntity = new CompInstKpiEntity();
        compInstKpiEntity.setIsGroup(false);
        compInstKpiEntity.setIsInfo(1);
        compInstKpiEntity.setKpiViolationConfig(violationProfiles);

        KpiDetails kpiDetails = new KpiDetails();
        kpiDetails.setKpiViolationConfig(violationProfiles);

        cacheWrapperMock = Mockito.mock(CacheWrapper.class);
        Mockito.when(cacheWrapperMock.getInstanceKPIDetails(accountIdentifier, instanceIdentifier, kpiId))
                .thenReturn(compInstKpiEntity);
        process.cacheWrapper = cacheWrapperMock;
        Mockito.when(process.cacheWrapper.getInstanceKPIDetails(accountIdentifier, instanceIdentifier, kpiId))
                .thenReturn(compInstKpiEntity);

        Mockito.when(cacheWrapperMock.getServiceKPIDetails(accountIdentifier, instanceIdentifier, kpiId))
                .thenReturn(kpiDetails);
        process.cacheWrapper = cacheWrapperMock;
        Mockito.when(process.cacheWrapper.getServiceKPIDetails(accountIdentifier, instanceIdentifier, kpiId))
                .thenReturn(kpiDetails);

        Method method = GetViolatedData.class.getDeclaredMethod(
                "getViolatedDataListForAllViolations",
                ViolatedData.class);
        method.setAccessible(true);
        @SuppressWarnings("unchecked")
        List<ViolatedData> result = (List<ViolatedData>) method.invoke(process, violatedData);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("431", result.get(0).getThresholdSeverity());
        assertEquals("433", result.get(1).getThresholdSeverity());
    }

    @Test
    void testCheckViolations_GetHighestSeverity_TxnViolation() throws Exception {
        GetViolatedData process = new GetViolatedData();
        String accountIdentifier =  "demo";
        int kpiId = 1;
        String instanceIdentifier = "1fa5190c-ce1f-4da0-9f72-51e7d70f73ca";

        ViolatedData violatedData = new ViolatedData(accountIdentifier, Arrays.asList("app1"));
        violatedData.setEventType(ViolationEventType.TXN_VIOLATION);
        violatedData.setKpiId(String.valueOf(kpiId));
        violatedData.setInstanceId(instanceIdentifier);
        violatedData.setKpiAttribute(Constants.COMMON_ATTRIBUTE);
        violatedData.setValue("55.0");
        violatedData.setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);
        Map<String, String> metaData = new HashMap<>();
        metaData.put("violationLevel", "INSTANCE");
        violatedData.setMetaData(metaData);

        String attributeName = "ALL";
        List<KpiViolationConfig> configList = new ArrayList<>();
        KpiViolationConfig config = KpiViolationConfig.builder()
                .operation("not between")
                .minThreshold(30.0)
                .maxThreshold(40.0)
                .status(1)
                .generateAnomaly(1)
                .excludeMaintenance(1)
                .thresholdSeverity("Low")
                .thresholdSeverityId(431)
                .build();
        configList.add(config);
        config = KpiViolationConfig.builder()
                .operation("not between")
                .minThreshold(50.0)
                .maxThreshold(60.0)
                .status(1)
                .generateAnomaly(1)
                .excludeMaintenance(1)
                .thresholdSeverity("Medium")
                .thresholdSeverityId(432)
                .build();
        configList.add(config);
        config = KpiViolationConfig.builder()
                .operation("not between")
                .minThreshold(70.0)
                .maxThreshold(80.0)
                .status(1)
                .generateAnomaly(1)
                .excludeMaintenance(1)
                .thresholdSeverity("High")
                .thresholdSeverityId(433)
                .build();
        configList.add(config);
        Map<String, List<KpiViolationConfig>> violationProfiles = new HashMap<>();
        violationProfiles.put(attributeName, configList);

        CompInstKpiEntity compInstKpiEntity = new CompInstKpiEntity();
        compInstKpiEntity.setIsGroup(false);
        compInstKpiEntity.setIsInfo(1);
        compInstKpiEntity.setKpiViolationConfig(violationProfiles);

        KpiDetails kpiDetails = new KpiDetails();
        kpiDetails.setKpiViolationConfig(violationProfiles);

        cacheWrapperMock = Mockito.mock(CacheWrapper.class);
        Mockito.when(cacheWrapperMock.getInstanceKPIDetails(accountIdentifier, instanceIdentifier, kpiId))
                .thenReturn(compInstKpiEntity);
        process.cacheWrapper = cacheWrapperMock;
        Mockito.when(process.cacheWrapper.getInstanceKPIDetails(accountIdentifier, instanceIdentifier, kpiId))
                .thenReturn(compInstKpiEntity);

        Mockito.when(cacheWrapperMock.getServiceKPIDetails(accountIdentifier, instanceIdentifier, kpiId))
                .thenReturn(kpiDetails);
        process.cacheWrapper = cacheWrapperMock;
        Mockito.when(process.cacheWrapper.getServiceKPIDetails(accountIdentifier, instanceIdentifier, kpiId))
                .thenReturn(kpiDetails);

        Method method = GetViolatedData.class.getDeclaredMethod(
                "getViolatedDataListForAllViolations",
                ViolatedData.class);
        method.setAccessible(true);
        @SuppressWarnings("unchecked")
        List<ViolatedData> result = (List<ViolatedData>) method.invoke(process, violatedData);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("431", result.get(0).getThresholdSeverity());
        assertEquals("433", result.get(1).getThresholdSeverity());
    }
}
