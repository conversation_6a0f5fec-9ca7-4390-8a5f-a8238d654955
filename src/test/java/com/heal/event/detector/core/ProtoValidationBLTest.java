package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.*;
import com.heal.event.detector.pojos.GenericValidationObject;
import com.heal.event.detector.pojos.ViolatedData;
import com.heal.event.detector.service.EventForwarderToQueue;
import com.heal.event.detector.util.ProtoCreator;
import com.heal.event.detector.utility.HealthMetrics;
import nl.altindag.log.LogCaptor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest
public class ProtoValidationBLTest {

    @Spy
    private ProtoValidation protoValidation;

    @Autowired
    ProtoCreator protoCreator;

    @Spy
    ViolatedEventsProcess violatedEventsProcess;

    @Mock
    ViolatedEventsProcess violatedEventsProcessMock;

    @Spy
    AnomalyEventsProcess anomalyEventsProcess;

    @Mock
    AnomalyEventsProcess anomalyEventsProcessMock;

    @Spy
    ThresholdDataProcess thresholdDataProcess;

    @Mock
    GetViolatedData getViolatedDataMock;

    @Mock
    EventForwarderToQueue forwarderMock;

    @Mock
    HealthMetrics metricsMock;

    @BeforeEach
    void initialize() {
        protoValidation.anomalyEventsProcess = anomalyEventsProcess;
        protoValidation.anomalyEventsProcess.outOfOrderValue = 5;
        protoValidation.anomalyEventsProcess.metrics = metricsMock;
        protoValidation.violatedEventsProcess = violatedEventsProcess;
        protoValidation.violatedEventsProcess.outOfOrderValue = 5;
        protoValidation.violatedEventsProcess.metrics = metricsMock;
        protoValidation.thresholdDataProcess = thresholdDataProcess;
        protoValidation.thresholdDataProcess.metrics = metricsMock;
        protoValidation.getViolatedData = getViolatedDataMock;
        protoValidation.forwarder = forwarderMock;
        protoValidation.metrics = metricsMock;
    }

    @Test
    void validateAndProcessInputAggregatedKPI_NullParameter() {

        GenericValidationObject<AggregatedKpiProtos.AggregatedKpi> aggResult =
                GenericValidationObject.<AggregatedKpiProtos.AggregatedKpi>builder()
                        .proto(null)
                        .isValid(false)
                        .build();
        assertThat(violatedEventsProcess.validateAggregatedKpiData(null)).isEqualTo(aggResult);

    }

    @Test
    void validateAndProcessInputAggregatedKPI_AggregatedKpiProto_SizeZero() {

        GenericValidationObject<AggregatedKpiProtos.AggregatedKpi> aggResult =
                GenericValidationObject.<AggregatedKpiProtos.AggregatedKpi>builder()
                        .proto(AggregatedKpiProtos.AggregatedKpi.newBuilder().build())
                        .isValid(false)
                        .build();
        assertThat(violatedEventsProcess.validateAggregatedKpiData(AggregatedKpiProtos.AggregatedKpi.newBuilder().build()))
                .isEqualTo(aggResult);

    }

    @Test
    void validateAndProcessInputAggregatedKPI_AggregatedKpiProto_AccountEmpty() {

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator.createAggregatedKPIBodyNonGrpKpiType("",
                "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", 60,
                "RPS", 60, 19800, "50",
                KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        GenericValidationObject<AggregatedKpiProtos.AggregatedKpi> aggResult =
                GenericValidationObject.<AggregatedKpiProtos.AggregatedKpi>builder()
                        .proto(aggregatedKpi)
                        .isValid(false)
                        .build();

        assertThat(violatedEventsProcess.validateAggregatedKpiData(aggregatedKpi)).isEqualTo(aggResult);

    }

    @Test
    void validateAndProcessInputAggregatedKPI_AggregatedKpiProto_ServiceIdListEmpty() {

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator.createAggregatedKPIBodyNonGrpKpiType("d681ef13-d690-4917-jkhg-6c79b-9",
                "NB-Web-Service-DR", "", "PC_HTTPD_233", 60,
                "RPS", 60, 19800, "50",
                KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);
        aggregatedKpi = aggregatedKpi.toBuilder().clearServiceId()
                .addAllServiceId(new ArrayList<>()).build();

        GenericValidationObject<AggregatedKpiProtos.AggregatedKpi> aggResult =
                GenericValidationObject.<AggregatedKpiProtos.AggregatedKpi>builder()
                        .proto(aggregatedKpi)
                        .isValid(false)
                        .build();

        assertThat(violatedEventsProcess.validateAggregatedKpiData(aggregatedKpi)).isEqualTo(aggResult);

    }

    @Test
    void validateAndProcessInputAggregatedKPI_AggregatedKpiProto_AllServiceInServiceIdListEmpty() {

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator.createAggregatedKPIBodyNonGrpKpiType("d681ef13-d690-4917-jkhg-6c79b-9",
                "NB-Web-Service-DR", " ", "PC_HTTPD_233", 60,
                "RPS", 60, 19800, "50",
                KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        GenericValidationObject<AggregatedKpiProtos.AggregatedKpi> aggResult =
                GenericValidationObject.<AggregatedKpiProtos.AggregatedKpi>builder()
                        .proto(aggregatedKpi)
                        .isValid(false)
                        .build();

        assertThat(violatedEventsProcess.validateAggregatedKpiData(aggregatedKpi)).isEqualTo(aggResult);

    }

    @Test
    void validateAndProcessInputAggregatedKPI_AggregatedKpiProto_OneServiceInServiceIdListEmpty() {

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator.createAggregatedKPIBodyNonGrpKpiType
                ("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-Service-DR", "sample, ", "PC_HTTPD_233", 60,
                        "RPS", 60, 19800, "50",
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        GenericValidationObject<AggregatedKpiProtos.AggregatedKpi> aggResult =
                GenericValidationObject.<AggregatedKpiProtos.AggregatedKpi>builder()
                        .proto(aggregatedKpi.toBuilder().clearServiceId()
                                .addAllServiceId(Collections.singletonList("sample")).build())
                        .isValid(true)
                        .build();

        assertThat(violatedEventsProcess.validateAggregatedKpiData(aggregatedKpi)).isEqualTo(aggResult);
    }

    @Test
    void validateAndProcessInputAggregatedKPI_AggregatedKpiProto_InstanceEmpty() {

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator.createAggregatedKPIBodyNonGrpKpiType("d681ef13-d690-4917-jkhg-6c79b-9",
                "NB-Web-App", "NB-Web-Service-DR", "", 60,
                "RPS", 60, 19800, "50",
                KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        GenericValidationObject<AggregatedKpiProtos.AggregatedKpi> aggResult =
                GenericValidationObject.<AggregatedKpiProtos.AggregatedKpi>builder()
                        .proto(aggregatedKpi)
                        .isValid(false)
                        .build();

        assertThat(violatedEventsProcess.validateAggregatedKpiData(aggregatedKpi)).isEqualTo(aggResult);

    }

    @Test
    void validateAndProcessInputAggregatedKPI_AggregatedKpiProto_ApplicationIdListEmpty() {

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator.createAggregatedKPIBodyNonGrpKpiType("d681ef13-d690-4917-jkhg-6c79b-9",
                "", "NB-Web-Service-DR", "PC_HTTPD_233", 60,
                "RPS", 60, 19800, "50",
                KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);
        aggregatedKpi = aggregatedKpi.toBuilder().clearApplicationId()
                .addAllApplicationId(new ArrayList<>()).build();

        GenericValidationObject<AggregatedKpiProtos.AggregatedKpi> aggResult =
                GenericValidationObject.<AggregatedKpiProtos.AggregatedKpi>builder()
                        .proto(aggregatedKpi)
                        .isValid(false)
                        .build();

        assertThat(violatedEventsProcess.validateAggregatedKpiData(aggregatedKpi)).isEqualTo(aggResult);

    }

    @Test
    void validateAndProcessInputAggregatedKPI_AggregatedKpiProto_AllApplicationInApplicationIdListEmpty() {

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator.createAggregatedKPIBodyNonGrpKpiType("d681ef13-d690-4917-jkhg-6c79b-9",
                "", "NB-Web-Service-DR", "PC_HTTPD_233", 60,
                "RPS", 60, 19800, "50",
                KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        GenericValidationObject<AggregatedKpiProtos.AggregatedKpi> aggResult =
                GenericValidationObject.<AggregatedKpiProtos.AggregatedKpi>builder()
                        .proto(aggregatedKpi)
                        .isValid(false)
                        .build();

        assertThat(violatedEventsProcess.validateAggregatedKpiData(aggregatedKpi)).isEqualTo(aggResult);

    }

    @Test
    void validateAndProcessInputAggregatedKPI_AggregatedKpiProto_OneApplicationInApplicationIdListEmpty() {

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator.createAggregatedKPIBodyNonGrpKpiType("d681ef13-d690-4917-jkhg-6c79b-9",
                "Sample, ", "NB-Web-Service-DR", "PC_HTTPD_233", 60,
                "RPS", 60, 19800, "50",
                KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        GenericValidationObject<AggregatedKpiProtos.AggregatedKpi> aggResult =
                GenericValidationObject.<AggregatedKpiProtos.AggregatedKpi>builder()
                        .proto(aggregatedKpi.toBuilder().clearApplicationId()
                                .addAllApplicationId(Collections.singletonList("Sample")).build())
                        .isValid(true)
                        .build();

        assertThat(violatedEventsProcess.validateAggregatedKpiData(aggregatedKpi)).isEqualTo(aggResult);

    }

    @Test
    void validateAndProcessInputAggregatedKPI_AggregatedKpiProto_kpiId_lteZero() {

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator.createAggregatedKPIBodyNonGrpKpiType("d681ef13-d690-4917-jkhg-6c79b-9",
                "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", 0,
                "RPS", 60, 19800, "50",
                KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        GenericValidationObject<AggregatedKpiProtos.AggregatedKpi> aggResult =
                GenericValidationObject.<AggregatedKpiProtos.AggregatedKpi>builder()
                        .proto(aggregatedKpi)
                        .isValid(false)
                        .build();

        assertThat(violatedEventsProcess.validateAggregatedKpiData(aggregatedKpi)).isEqualTo(aggResult);


        aggregatedKpi = protoCreator.createAggregatedKPIBodyNonGrpKpiType("d681ef13-d690-4917-jkhg-6c79b-9",
                "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", -10,
                "RPS", 60, 19800, "50",
                KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        aggResult = GenericValidationObject.<AggregatedKpiProtos.AggregatedKpi>builder()
                .proto(aggregatedKpi)
                .isValid(false)
                .build();

        assertThat(violatedEventsProcess.validateAggregatedKpiData(aggregatedKpi)).isEqualTo(aggResult);

    }

    @Test
    void validateAndProcessInputAggregatedKPI_AggregatedKpiProto_KpiNameEmpty() {

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator.createAggregatedKPIBodyNonGrpKpiType("d681ef13-d690-4917-jkhg-6c79b-9",
                "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", 60,
                "", 60, 19800, "50",
                KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        GenericValidationObject<AggregatedKpiProtos.AggregatedKpi> aggResult =
                GenericValidationObject.<AggregatedKpiProtos.AggregatedKpi>builder()
                        .proto(aggregatedKpi)
                        .isValid(false)
                        .build();

        assertThat(violatedEventsProcess.validateAggregatedKpiData(aggregatedKpi)).isEqualTo(aggResult);

    }

    @Test
    void validateAndProcessInputAggregatedKPI_AggregatedKpiProto_NonGrpKpiVal_LengthZero() {

        int kpiId = 60;
        String instanceId = "PC_HTTPD_233";
        String accountId = "d681ef13-d690-4917-jkhg-6c79b-9";

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator.createAggregatedKPIBodyNonGrpKpiType(accountId,
                "NB-Web-App", "NB-Web-Service-DR", instanceId, kpiId,
                "RPS", 60, 19800, "",
                KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        GenericValidationObject<AggregatedKpiProtos.AggregatedKpi> aggResult =
                GenericValidationObject.<AggregatedKpiProtos.AggregatedKpi>builder()
                        .proto(aggregatedKpi)
                        .isValid(false)
                        .build();

        assertThat(violatedEventsProcess.validateAggregatedKpiData(aggregatedKpi)).isEqualTo(aggResult);

    }

    @Test
    void validateAndProcessInputAggregatedKPI_AggregatedKpiProto_NonGrpKpiVal_InvalidValue() {

        int kpiId = 60;
        String instanceId = "PC_HTTPD_233";
        String accountId = "d681ef13-d690-4917-jkhg-6c79b-9";

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator.createAggregatedKPIBodyNonGrpKpiType(accountId,
                "NB-Web-App", "NB-Web-Service-DR", instanceId, kpiId,
                "RPS", 60, 19800, "80.aa",
                KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        GenericValidationObject<AggregatedKpiProtos.AggregatedKpi> aggResult =
                GenericValidationObject.<AggregatedKpiProtos.AggregatedKpi>builder()
                        .proto(aggregatedKpi)
                        .isValid(false)
                        .build();

        assertThat(violatedEventsProcess.validateAggregatedKpiData(aggregatedKpi)).isEqualTo(aggResult);

    }

    @Test
    void validateAndProcessInputAggregatedKPI_AggregatedKpiProto_GrpKpi_EmptyValPair() {

        int kpiId = 60;
        String instanceId = "PC_HTTPD_233";
        String accountId = "d681ef13-d690-4917-jkhg-6c79b-9";

        Map<String, String> grpKpiMap = new HashMap<>();

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator.createAggregatedKPIBodyGrpKpiType(accountId,
                "NB-Web-App", "NB-Web-Service-DR", instanceId, kpiId,
                "AVAIL_SIZE_MB", 60, 19800, grpKpiMap,
                KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        GenericValidationObject<AggregatedKpiProtos.AggregatedKpi> aggResult =
                GenericValidationObject.<AggregatedKpiProtos.AggregatedKpi>builder()
                        .proto(aggregatedKpi)
                        .isValid(false)
                        .build();

        assertThat(violatedEventsProcess.validateAggregatedKpiData(aggregatedKpi)).isEqualTo(aggResult);

    }

    @Test
    void validateAndProcessInputAggregatedKPI_AggDataProcess_EmptyViolatedDataFound() {
        LogCaptor logCaptorProtoValidation = LogCaptor.forClass(ProtoValidation.class);

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator.createAggregatedKPIBodyNonGrpKpiType("d681ef13-d690-4917-jkhg-6c79b-9",
                "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", 60,
                "RPS", 60, 19800, "50",
                KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        GenericValidationObject<AggregatedKpiProtos.AggregatedKpi> aggResult = GenericValidationObject.<AggregatedKpiProtos.AggregatedKpi>builder()
                .proto(aggregatedKpi)
                .isValid(true)
                .build();

        protoValidation.violatedEventsProcess = violatedEventsProcessMock;
        Mockito.when(protoValidation.violatedEventsProcess.validateAggregatedKpiData(aggregatedKpi))
                .thenReturn(aggResult);
        Mockito.when(protoValidation.violatedEventsProcess.processAggregatedKpiData(aggregatedKpi))
                .thenReturn(new ArrayList<>());

        protoValidation.validateAndProcessInputAggregatedKPI(aggregatedKpi);

        assert (logCaptorProtoValidation.getLogs().isEmpty());
    }

    @Test
    void validateAndProcessInputAggregatedKPI_SuccessAggDataProcess() {

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = protoCreator.createAggregatedKPIBodyNonGrpKpiType("d681ef13-d690-4917-jkhg-6c79b-9",
                "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", 60,
                "RPS", 60, 19800, "50",
                KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

        GenericValidationObject<AggregatedKpiProtos.AggregatedKpi> result =
                protoValidation.violatedEventsProcess.validateAggregatedKpiData(aggregatedKpi);

        assert result.isValid();
        assert result.getProto().equals(aggregatedKpi);

    }


    @Test
    void validateAndProcessInputViolatedEvents_NullParameter() {

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(null)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(null)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_SizeZero() {

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(ViolatedEventProtos.ViolatedEvent.newBuilder().build())
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(ViolatedEventProtos.ViolatedEvent.newBuilder().build()))
                .isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_AccountIdEmpty() {

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType("",
                        "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", "60",
                        "ALL", "60", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_ApplicationIdListEmpty() {

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "", "NB-Web-Service-DR", "PC_HTTPD_233", "60",
                        "ALL", "60", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());
        violatedEvent = violatedEvent.toBuilder().clearAppId().addAllAppId(new ArrayList<>()).build();

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_AllApplicationInApplicationIdListEmpty() {

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "", "NB-Web-Service-DR", "PC_HTTPD_233", "60",
                        "ALL", "60", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_OneApplicationInApplicationIdListEmpty() {

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "Simple, ", "NB-Web-Service-DR", "PC_HTTPD_233", "60",
                        "ALL", "60", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent.toBuilder()
                                .clearAppId().addAllAppId(Collections.singletonList("Simple")).build())
                        .isValid(true)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_ThresholdTypeEmpty() {

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", "60",
                        "ALL", "60", 19800, "",
                        "greater than", "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }


    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_kpiInfo_KpiIdEmpty() {

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", "",
                        "ALL", "60", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_kpiInfo_InstanceIdEmpty() {

        String kpiId = "60";
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "", kpiId,
                        "ALL", "60", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_kpiInfo_KpiAttributeEmpty() {

        String kpiId = "60";
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", kpiId,
                        "", "60", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_kpiInfo_OperationTypeEmpty() {

        String kpiId = "60";
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", kpiId,
                        "ALL", "60", 19800, "Static",
                        "", "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_kpiInfo_ServiceIdListEmpty() {

        String kpiId = "60";

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "", "PC_HTTPD_233", kpiId,
                        "ALL", "60", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());

        violatedEvent = violatedEvent.toBuilder().clearKpis()
                .addKpis(violatedEvent.getKpisList().get(0).toBuilder().clearKpiInfo()
                        .setKpiInfo(violatedEvent.getKpisList().get(0).getKpiInfo().toBuilder()
                                .clearSvcId().build()).build()).build();

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_kpiInfo_AllServiceInServiceIdListEmpty() {

        String kpiId = "60";
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "", "PC_HTTPD_233", kpiId,
                        "ALL", "60", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_kpiInfo_OneServiceInServiceIdListEmpty() {

        String kpiId = "60";
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "Sample, ", "PC_HTTPD_233", kpiId,
                        "ALL", "60", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());

        ViolatedEventProtos.ViolatedEvent violatedEventMod = violatedEvent.toBuilder().clearKpis()
                .addKpis(violatedEvent.getKpisList().get(0).toBuilder().clearKpiInfo()
                        .setKpiInfo(violatedEvent.getKpisList().get(0).getKpiInfo().toBuilder()
                                .clearSvcId().addAllSvcId(Collections.singletonList("Sample")).build()).build()).build();

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEventMod)
                        .isValid(true)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_kpiInfo_valueEmpty() {

        String kpiId = "60";
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", kpiId,
                        "ALL", "", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_kpiInfo_thresholdMapEmpty() {

        String kpiId = "60";
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", kpiId,
                        "ALL", "60", 19800, "Static",
                        "greater than", "Default", new HashMap<>());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }


    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_txnInfo_KpiIdEmpty() {

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventTxnInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "fldTxnMNU_P", "",
                        PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC, "504.0", "0",
                        19800, "Static", "greater than",
                        "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_txnInfo_transactionIdEmpty() {

        String kpiId = "403";
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventTxnInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "", kpiId,
                        PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC, "504.0", "0",
                        19800, "Static", "greater than",
                        "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_txnInfo_groupIdEmpty() {

        String kpiId = "403";
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventTxnInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "fldTxnMNU_P", kpiId,
                        PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC, "504.0", "",
                        19800, "Static", "greater than",
                        "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_txnInfo_OperationTypeEmpty() {

        String kpiId = "403";
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventTxnInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "fldTxnMNU_P", kpiId,
                        PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC, "504.0", "0",
                        19800, "Static", "",
                        "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_txnInfo_ServiceIdEmpty() {

        String kpiId = "403";
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventTxnInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "", "fldTxnMNU_P", kpiId,
                        PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC, "504.0", "0",
                        19800, "Static", "greater than",
                        "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_txnInfo_valueEmpty() {

        String kpiId = "403";
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventTxnInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "fldTxnMNU_P", kpiId,
                        PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC, "", "0",
                        19800, "Static", "greater than",
                        "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_txnInfo_thresholdMapEmpty() {

        String kpiId = "403";
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventTxnInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "fldTxnMNU_P", kpiId,
                        PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC, "504.0", "0",
                        19800, "Static", "greater than",
                        "Default", new HashMap<>());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }


    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_batchJobInfo_KpiIdEmpty() {

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventBatchJobInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "101", "", "504.0",
                        19800, "Static",
                        "greater than", "Default",
                        protoCreator.thresholdMapProvider(), protoCreator.metadataMapProvider());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_batchJobInfo_batchJobIdEmpty() {

        String kpiId = "403";
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventBatchJobInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "", kpiId, "504.0",
                        19800, "Static",
                        "greater than", "Default",
                        protoCreator.thresholdMapProvider(), protoCreator.metadataMapProvider());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_batchJobInfo_OperationTypeEmpty() {

        String kpiId = "403";
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventBatchJobInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "101", kpiId, "504.0",
                        19800, "Static",
                        "", "Default",
                        protoCreator.thresholdMapProvider(), protoCreator.metadataMapProvider());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_batchJobInfo_valueEmpty() {

        String kpiId = "403";
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventBatchJobInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "101", kpiId, "",
                        19800, "Static",
                        "greater than", "Default",
                        protoCreator.thresholdMapProvider(), protoCreator.metadataMapProvider());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_batchJobInfo_thresholdMapEmpty() {

        String kpiId = "403";
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventBatchJobInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "101", kpiId, "504.0",
                        19800, "Static",
                        "greater than", "Default",
                        new HashMap<>(), protoCreator.metadataMapProvider());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedEventProto_kpiInfo_txnInfo_batchJobInfo_Empty() {

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", "",
                        "ALL", "60", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());
        violatedEvent = violatedEvent.toBuilder().clearKpis().build();

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(false)
                        .build();

        assertThat(anomalyEventsProcess.validateAnomalyEvent(violatedEvent)).isEqualTo(violatedResult);

    }


    @Test
    void validateAndProcessInputViolatedEvents_ViolatedDataProcess_EmptyAnomalyEventFound() {
        LogCaptor logCaptorProtoValidation = LogCaptor.forClass(ProtoValidation.class);

        String kpiId = "60";
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", kpiId,
                        "ALL", "60", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());
        ViolatedData violatedData = protoCreator
                .createViolatedDataKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", kpiId,
                        "ALL", "320.0", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> violatedResult =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .proto(violatedEvent)
                        .isValid(true)
                        .build();

        Mockito.when(protoValidation.getViolatedData.getViolatedDataObject(violatedEvent))
                .thenReturn(Collections.singletonList(violatedData));
        protoValidation.anomalyEventsProcess = anomalyEventsProcessMock;
        Mockito.when(protoValidation.anomalyEventsProcess.validateAnomalyEvent(violatedEvent))
                .thenReturn(violatedResult);
        Mockito.when(protoValidation.anomalyEventsProcess.processViolatedKpiData(Collections.singletonList(violatedData)))
                .thenReturn(new ArrayList<>());

        protoValidation.validateAndProcessInputViolatedEvents(violatedEvent);

        assert (logCaptorProtoValidation.getLogs().isEmpty());
    }

    @Test
    void validateAndProcessInputViolatedEvents_ViolatedDataProcess_EmptyViolatedDataFound() {
        LogCaptor logCaptorProtoValidation = LogCaptor.forClass(ProtoValidation.class);

        String kpiId = "60";
        String accountId = "d681ef13-d690-4917-jkhg-6c79b-9";
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountId,
                        "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", kpiId,
                        "ALL", "60", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());

        Mockito.when(protoValidation.getViolatedData.getViolatedDataObject(violatedEvent))
                .thenReturn(new ArrayList<>());

        protoValidation.validateAndProcessInputViolatedEvents(violatedEvent);

        assertThat(logCaptorProtoValidation.getErrorLogs().get(0)).contains("Valid violated data unavailable for account [" + accountId + "], kpi list");
    }

    @Test
    void validateAndProcessInputViolatedEvents_SuccessViolatedDataProcess() {

        String kpiId = "60";
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", kpiId,
                        "ALL", "60", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());
        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> result =
                protoValidation.anomalyEventsProcess.validateAnomalyEvent(violatedEvent);

        assert result.isValid();
        assert result.getProto().equals(violatedEvent);

    }


    @Test
    void validateAndProcessInputNorThresholdEvents_NullParameter() {

        GenericValidationObject<ThresholdProtos.Threshold> thresholdResult =
                GenericValidationObject.<ThresholdProtos.Threshold>builder()
                        .proto(null)
                        .isValid(false)
                        .build();

        assertThat(thresholdDataProcess.validateNorThreshold(null)).isEqualTo(thresholdResult);

    }

    @Test
    void validateAndProcessInputNorThresholdEvents_ThresholdProto_SizeZero() {

        GenericValidationObject<ThresholdProtos.Threshold> thresholdResult =
                GenericValidationObject.<ThresholdProtos.Threshold>builder()
                        .proto(ThresholdProtos.Threshold.newBuilder().build())
                        .isValid(false)
                        .build();

        assertThat(thresholdDataProcess.validateNorThreshold(ThresholdProtos.Threshold.newBuilder().build()))
                .isEqualTo(thresholdResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ThresholdProto_AccountIdEmpty() {

        ThresholdProtos.Threshold threshold = protoCreator
                .createThresholdProtoKpiInfoType("",
                        "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", "60",
                        "ALL", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ThresholdProtos.Threshold> thresholdResult =
                GenericValidationObject.<ThresholdProtos.Threshold>builder()
                        .proto(threshold)
                        .isValid(false)
                        .build();

        assertThat(thresholdDataProcess.validateNorThreshold(threshold)).isEqualTo(thresholdResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ThresholdProto_ApplicationIdEmpty() {

        ThresholdProtos.Threshold threshold = protoCreator
                .createThresholdProtoKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "", "NB-Web-Service-DR", "PC_HTTPD_233", "60",
                        "ALL", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ThresholdProtos.Threshold> thresholdResult =
                GenericValidationObject.<ThresholdProtos.Threshold>builder()
                        .proto(threshold)
                        .isValid(false)
                        .build();

        assertThat(thresholdDataProcess.validateNorThreshold(threshold)).isEqualTo(thresholdResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ThresholdProto_ThresholdTypeEmpty() {

        ThresholdProtos.Threshold threshold = protoCreator
                .createThresholdProtoKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", "60",
                        "ALL", 19800, "",
                        "greater than", "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ThresholdProtos.Threshold> thresholdResult =
                GenericValidationObject.<ThresholdProtos.Threshold>builder()
                        .proto(threshold)
                        .isValid(false)
                        .build();

        assertThat(thresholdDataProcess.validateNorThreshold(threshold)).isEqualTo(thresholdResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ThresholdProto_kpiInfo_KpiIdEmpty() {

        ThresholdProtos.Threshold threshold = protoCreator
                .createThresholdProtoKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", "",
                        "ALL", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ThresholdProtos.Threshold> thresholdResult =
                GenericValidationObject.<ThresholdProtos.Threshold>builder()
                        .proto(threshold)
                        .isValid(false)
                        .build();

        assertThat(thresholdDataProcess.validateNorThreshold(threshold)).isEqualTo(thresholdResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ThresholdProto_kpiInfo_InstanceIdEmpty() {

        String kpiId = "60";
        ThresholdProtos.Threshold threshold = protoCreator
                .createThresholdProtoKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "", kpiId,
                        "ALL", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ThresholdProtos.Threshold> thresholdResult =
                GenericValidationObject.<ThresholdProtos.Threshold>builder()
                        .proto(threshold)
                        .isValid(false)
                        .build();

        assertThat(thresholdDataProcess.validateNorThreshold(threshold)).isEqualTo(thresholdResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ThresholdProto_kpiInfo_KpiAttributeEmpty() {

        String kpiId = "60";
        ThresholdProtos.Threshold threshold = protoCreator
                .createThresholdProtoKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", kpiId,
                        "", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ThresholdProtos.Threshold> thresholdResult =
                GenericValidationObject.<ThresholdProtos.Threshold>builder()
                        .proto(threshold)
                        .isValid(false)
                        .build();

        assertThat(thresholdDataProcess.validateNorThreshold(threshold)).isEqualTo(thresholdResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ThresholdProto_kpiInfo_OperationTypeEmpty() {

        String kpiId = "60";
        ThresholdProtos.Threshold threshold = protoCreator
                .createThresholdProtoKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", kpiId,
                        "ALL", 19800, "Static",
                        "", "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ThresholdProtos.Threshold> thresholdResult =
                GenericValidationObject.<ThresholdProtos.Threshold>builder()
                        .proto(threshold)
                        .isValid(false)
                        .build();

        assertThat(thresholdDataProcess.validateNorThreshold(threshold)).isEqualTo(thresholdResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ThresholdProto_kpiInfo_ServiceIdListEmpty() {

        String kpiId = "60";
        ThresholdProtos.Threshold threshold = protoCreator
                .createThresholdProtoKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "", "PC_HTTPD_233", kpiId,
                        "ALL", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());
        threshold = threshold.toBuilder().clearKpiThreshold()
                .addKpiThreshold(threshold.getKpiThresholdList().get(0).toBuilder().clearSvcId().build()).build();

        GenericValidationObject<ThresholdProtos.Threshold> thresholdResult =
                GenericValidationObject.<ThresholdProtos.Threshold>builder()
                        .proto(threshold)
                        .isValid(false)
                        .build();

        assertThat(thresholdDataProcess.validateNorThreshold(threshold)).isEqualTo(thresholdResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ThresholdProto_kpiInfo_AllServiceInServiceIdListEmpty() {

        String kpiId = "60";
        ThresholdProtos.Threshold threshold = protoCreator
                .createThresholdProtoKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "", "PC_HTTPD_233", kpiId,
                        "ALL", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ThresholdProtos.Threshold> thresholdResult =
                GenericValidationObject.<ThresholdProtos.Threshold>builder()
                        .proto(threshold)
                        .isValid(false)
                        .build();

        assertThat(thresholdDataProcess.validateNorThreshold(threshold)).isEqualTo(thresholdResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ThresholdProto_kpiInfo_OneServiceInServiceIdListEmpty() {

        String kpiId = "60";
        ThresholdProtos.Threshold threshold = protoCreator
                .createThresholdProtoKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "Sample, ", "PC_HTTPD_233", kpiId,
                        "ALL", 19800, "Static",
                        "greater than", "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ThresholdProtos.Threshold> thresholdResult =
                GenericValidationObject.<ThresholdProtos.Threshold>builder()
                        .proto(threshold.toBuilder().clearKpiThreshold()
                                .addKpiThreshold(threshold.getKpiThresholdList().get(0).toBuilder()
                                        .clearSvcId().addAllSvcId(Collections.singletonList("Sample"))
                                        .build()).build())
                        .isValid(true)
                        .build();

        assertThat(thresholdDataProcess.validateNorThreshold(threshold)).isEqualTo(thresholdResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ThresholdProto_kpiInfo_thresholdMapEmpty() {

        String kpiId = "60";
        ThresholdProtos.Threshold threshold = protoCreator
                .createThresholdProtoKpiInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", kpiId,
                        "ALL", 19800, "Static",
                        "greater than", "Default", new HashMap<>());

        GenericValidationObject<ThresholdProtos.Threshold> thresholdResult =
                GenericValidationObject.<ThresholdProtos.Threshold>builder()
                        .proto(threshold)
                        .isValid(false)
                        .build();

        assertThat(thresholdDataProcess.validateNorThreshold(threshold)).isEqualTo(thresholdResult);

    }


    @Test
    void validateAndProcessInputViolatedEvents_ThresholdProto_txnInfo_KpiIdEmpty() {

        ThresholdProtos.Threshold threshold = protoCreator
                .createThresholdProtoTxnInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "fldTxnMNU_P", "",
                        PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC, "0",
                        19800, "Static", "greater than",
                        "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ThresholdProtos.Threshold> thresholdResult =
                GenericValidationObject.<ThresholdProtos.Threshold>builder()
                        .proto(threshold)
                        .isValid(false)
                        .build();

        assertThat(thresholdDataProcess.validateNorThreshold(threshold)).isEqualTo(thresholdResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ThresholdProto_txnInfo_transactionIdEmpty() {

        String kpiId = "403";
        ThresholdProtos.Threshold threshold = protoCreator
                .createThresholdProtoTxnInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "", kpiId,
                        PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC, "0",
                        19800, "Static", "greater than",
                        "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ThresholdProtos.Threshold> thresholdResult =
                GenericValidationObject.<ThresholdProtos.Threshold>builder()
                        .proto(threshold)
                        .isValid(false)
                        .build();

        assertThat(thresholdDataProcess.validateNorThreshold(threshold)).isEqualTo(thresholdResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ThresholdProto_txnInfo_groupIdEmpty() {

        String kpiId = "403";
        ThresholdProtos.Threshold threshold = protoCreator
                .createThresholdProtoTxnInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "fldTxnMNU_P", kpiId,
                        PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC, "",
                        19800, "Static", "greater than",
                        "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ThresholdProtos.Threshold> thresholdResult =
                GenericValidationObject.<ThresholdProtos.Threshold>builder()
                        .proto(threshold)
                        .isValid(false)
                        .build();

        assertThat(thresholdDataProcess.validateNorThreshold(threshold)).isEqualTo(thresholdResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ThresholdProto_txnInfo_OperationTypeEmpty() {

        String kpiId = "403";
        ThresholdProtos.Threshold threshold = protoCreator
                .createThresholdProtoTxnInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "fldTxnMNU_P", kpiId,
                        PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC, "0",
                        19800, "Static", "",
                        "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ThresholdProtos.Threshold> thresholdResult =
                GenericValidationObject.<ThresholdProtos.Threshold>builder()
                        .proto(threshold)
                        .isValid(false)
                        .build();

        assertThat(thresholdDataProcess.validateNorThreshold(threshold)).isEqualTo(thresholdResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ThresholdProto_txnInfo_ServiceIdEmpty() {

        String kpiId = "403";
        ThresholdProtos.Threshold threshold = protoCreator
                .createThresholdProtoTxnInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "", "fldTxnMNU_P", kpiId,
                        PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC, "0",
                        19800, "Static", "greater than",
                        "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ThresholdProtos.Threshold> thresholdResult =
                GenericValidationObject.<ThresholdProtos.Threshold>builder()
                        .proto(threshold)
                        .isValid(false)
                        .build();

        assertThat(thresholdDataProcess.validateNorThreshold(threshold)).isEqualTo(thresholdResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ThresholdProto_txnInfo_thresholdMapEmpty() {

        ThresholdProtos.Threshold threshold = protoCreator
                .createThresholdProtoTxnInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "fldTxnMNU_P", "403",
                        PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC, "0",
                        19800, "Static", "greater than",
                        "Default", new HashMap<>());

        GenericValidationObject<ThresholdProtos.Threshold> thresholdResult =
                GenericValidationObject.<ThresholdProtos.Threshold>builder()
                        .proto(threshold)
                        .isValid(false)
                        .build();

        assertThat(thresholdDataProcess.validateNorThreshold(threshold)).isEqualTo(thresholdResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_ThresholdProto_kpiInfo_txnInfo_Empty() {

        ThresholdProtos.Threshold threshold = protoCreator
                .createThresholdProtoTxnInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "fldTxnMNU_P", "403",
                        PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC, "0",
                        19800, "Static", "greater than",
                        "Default", new HashMap<>());
        threshold = threshold.toBuilder().clearKpiThreshold().clearTransactionThreshold().build();

        GenericValidationObject<ThresholdProtos.Threshold> thresholdResult =
                GenericValidationObject.<ThresholdProtos.Threshold>builder()
                        .proto(threshold)
                        .isValid(false)
                        .build();

        protoValidation.validateAndProcessInputNorThresholdEvents(threshold);

        assertThat(thresholdDataProcess.validateNorThreshold(threshold)).isEqualTo(thresholdResult);

    }

    @Test
    void validateAndProcessInputViolatedEvents_SuccessThresholdDataProcess() {

        ThresholdProtos.Threshold threshold = protoCreator
                .createThresholdProtoTxnInfoType("d681ef13-d690-4917-jkhg-6c79b-9",
                        "NB-Web-App", "NB-Web-Service-DR", "fldTxnMNU_P", "403",
                        PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC, "0",
                        19800, "Static", "greater than",
                        "Default", protoCreator.thresholdMapProvider());

        GenericValidationObject<ThresholdProtos.Threshold> result =
                thresholdDataProcess.validateNorThreshold(threshold);

        assert result.isValid();
        assert result.getProto().equals(threshold);

    }

}
