package com.heal.event.detector.util;

import com.appnomic.appsone.common.protbuf.AggregatedKpiProtos;
import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeoutException;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class DataPusherToRMQ {
    public static void main(String[] args) {
        String host = "**************";
        int port = 5672;
        for (int i = 0; i < 10; i++) {
            try {
                DataPusherToRMQ.pushToSOR(host, port, 0, "TLSv1.2");
            } catch (IOException | TimeoutException e) {
                log.error("Error occurred while pushing data to RMQ. Details: ",e);

            }
        }

    }

    public static void pushToSOR(String host, int port, int option, String tlsVersion) throws IOException, TimeoutException {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(host);
        factory.setPort(port);
        if (tlsVersion != null) {
            try {
                factory.useSslProtocol(tlsVersion);
            } catch (NoSuchAlgorithmException | KeyManagementException e) {
                log.error("Error occurred while setting SSL protocol details for RMQ connection. Details: ",e);
            }
        }
        factory.setVirtualHost("/");
        factory.setAutomaticRecoveryEnabled(true);
        factory.setConnectionTimeout(5000);

        Connection connection = factory.newConnection();
        Channel channel = connection.createChannel();
        channel.queueDeclare("static-aggregated-kpi", true, false, false, null);

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi;
        switch (option) {
            case 0:
                aggregatedKpi = withInstanceKpiConfigMapNonNullNonGrpKpi();
                break;
            case 1:
                aggregatedKpi = withInstanceKpiConfigMapNonNullGrpKpi();
                break;
            case 2:
                aggregatedKpi = withInstanceLevelMaintenance();
                break;
            default:
                aggregatedKpi = null;
        }

        ByteArrayOutputStream os = new ByteArrayOutputStream();
        if (aggregatedKpi != null) {
            aggregatedKpi.writeDelimitedTo(os);
        }
        channel.basicPublish("", "static-aggregated-kpi", null, os.toByteArray());
//        channel.basicPublish("", "static-aggregated-kpi", null, aggregatedKpi.toByteArray());

        System.out.println("Data inserted into static-aggregated-kpi queue");
        channel.close();
        connection.close();
    }

    private static AggregatedKpiProtos.AggregatedKpi withInstanceKpiConfigMapNonNullNonGrpKpi() {
        ProtoCreator protoCreator = new ProtoCreator();
        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String instanceIdentifier = "SOLARIS_ENET_HOST_112_Inst_1-DC";
        int kpiId = 8;
        String serviceIdentifier = "ENET-App-Service-DC";

        return protoCreator
                .createAggregatedKPIBodyNonGrpKpiType(accountIdentifier,
                        "enet_3_DC", serviceIdentifier, instanceIdentifier, kpiId,
                        "CPU Busy", 60, 19800, "71",
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);
    }

    private static AggregatedKpiProtos.AggregatedKpi withInstanceKpiConfigMapNonNullGrpKpi() {
        ProtoCreator protoCreator = new ProtoCreator();
        Map<String, String> grpKpiMap = new HashMap<>();
        grpKpiMap.put("ALL", "1500");
        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String instanceIdentifier = "SOLARIS_ENET_HOST_112_Inst_1-DR";
        int kpiId = 8;
        String serviceIdentifier = "ENET-App-Service-DR";


        return protoCreator
                .createAggregatedKPIBodyGrpKpiType(accountIdentifier,
                        "enet_3_DR", serviceIdentifier, instanceIdentifier, kpiId,
                        "Available Size Host (MB)", 60, 19800, grpKpiMap,
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);
    }

    private static AggregatedKpiProtos.AggregatedKpi withInstanceLevelMaintenance() {
        ProtoCreator protoCreator = new ProtoCreator();
        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        int kpiId = 180;
        String instanceIdentifier = "ORACLE_ENET_DB_112_Inst_1-DC";
        String serviceIdentifier = "ENET-DB-Service-DC";
        String applicationIdentifier = "enet_3_DC";

        return protoCreator
                .createAggregatedKPIBodyNonGrpKpiType(accountIdentifier,
                        applicationIdentifier, serviceIdentifier, instanceIdentifier, kpiId,
                        "CPU Usage", 60, 19800, "80",
                        KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);

    }
}
