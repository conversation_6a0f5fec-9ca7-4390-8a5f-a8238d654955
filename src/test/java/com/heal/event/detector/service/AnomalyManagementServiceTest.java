package com.heal.event.detector.service;

import com.heal.configuration.protbuf.AnomalySummaryProtos;
import com.heal.event.detector.pojos.AnomalyRequest;
import com.heal.event.detector.pojos.AnomalyResponse;
import com.heal.event.detector.repo.OpenSearchRepo;
import com.heal.event.detector.repo.RedisUtilities;
import com.heal.event.detector.utility.HealthMetrics;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Test class for AnomalyManagementService
 */
@ExtendWith(MockitoExtension.class)
class AnomalyManagementServiceTest {

    @Mock
    private RabbitTemplate rabbitTemplate;

    @Mock
    private RedisUtilities redisUtilities;

    @Mock
    private OpenSearchRepo openSearchRepo;

    @Mock
    private HealthMetrics metrics;

    @InjectMocks
    private AnomalyManagementService anomalyManagementService;

    private AnomalyRequest testAnomalyRequest;

    private AnomalySummaryProtos.AnomalySummary anomalySummary;

    @BeforeEach
    void setUp() {
        testAnomalyRequest = AnomalyRequest.builder()
                .anomalyId("TEST_ANOMALY_001")
                .accountIdentifier("test-account")
                .entityId("test-instance")
                .kpiId(12345L)
                .kpiAttribute("CPU_USAGE")
                .categoryId("PERFORMANCE")
                .serviceId(Set.of("service-1", "service-2"))
                .thresholdType("STATIC")
                .operationType("GREATER_THAN")
                .value(String.valueOf(85.5))
                .lastThresholdsMeet(Map.of("warning", 70.0, "critical", 90.0))
                .metadata(Map.of("source", "test", "environment", "dev"))
                .anomalyTime(System.currentTimeMillis())
                .identifiedTime(System.currentTimeMillis())
                .kpiIdentifier("cpu-usage-kpi")
                .lastSeverityId(433)
                .build();
        AnomalySummaryProtos.AnomalySummary anomalySummary = AnomalySummaryProtos.AnomalySummary.newBuilder()
                .setAnomalyId(testAnomalyRequest.getAnomalyId())
                .setAccountIdentifier(testAnomalyRequest.getAccountIdentifier())
                .setEntityIdentifier(testAnomalyRequest.getEntityId())
                .setKpiId(String.valueOf(testAnomalyRequest.getKpiId()))
                .setKpiAttribute(testAnomalyRequest.getKpiAttribute())
                .build();
    }

    @Test
    void testCreateAnomaly_Success() {
        //TODO
//        doNothing().when(redisUtilities).storeAnomalyData(anyString(), anyString(), any(AnomalyRequest.class));
        doNothing().when(openSearchRepo).insertOrUpdateAnomalyData(anyString(), any(AnomalyRequest.class));
        doNothing().when(rabbitTemplate).convertAndSend(anyString(), anyString());
        doNothing().when(metrics).updateSnapshots(anyString(), anyInt());

        // When
        AnomalyResponse response = anomalyManagementService.createAnomaly(testAnomalyRequest, false);

        // Then
        assertTrue(response.isSuccess());
        assertEquals("Open", response.getAnomalyStatus());
        assertTrue(response.isRedisProcessed());
        assertTrue(response.isOpenSearchProcessed());
        assertTrue(response.isRabbitMqProcessed());

        //TODO
//        verify(redisUtilities).storeAnomalyData(eq(testAnomalyRequest.getAnomalyId()),
//                                               eq(testAnomalyRequest.getAccountIdentifier()),
//                                               any(AnomalyRequest.class));
        verify(openSearchRepo).insertOrUpdateAnomalyData(eq(testAnomalyRequest.getAccountIdentifier()),
                                                        any(AnomalyRequest.class));
        verify(rabbitTemplate).convertAndSend(anyString(), anyString());
    }

    @Test
    void testUpdateAnomaly_Success() {
        // Given
        AnomalyRequest existingAnomaly = AnomalyRequest.builder()
                .anomalyId(testAnomalyRequest.getAnomalyId())
                .accountIdentifier(testAnomalyRequest.getAccountIdentifier())
                .anomalyStatus("Open")
                .build();

        //TODO
//        when(redisUtilities.getAnomalyData(anyString(), anyString())).thenReturn(existingAnomaly);
//        doNothing().when(redisUtilities).storeAnomalyData(anyString(), anyString(), any(AnomalyRequest.class));
        doNothing().when(openSearchRepo).insertOrUpdateAnomalyData(anyString(), any(AnomalyRequest.class));
        doNothing().when(rabbitTemplate).convertAndSend(anyString(), anyString());

        // When
        AnomalyResponse response = anomalyManagementService.updateAnomaly(testAnomalyRequest);

        // Then
        assertTrue(response.isSuccess());
        assertEquals("Ongoing", response.getAnomalyStatus());
    }

    @Test
    void testCloseAnomaly_Success() {
        // Given
        AnomalyRequest existingAnomaly = AnomalyRequest.builder()
                .anomalyId(testAnomalyRequest.getAnomalyId())
                .accountIdentifier(testAnomalyRequest.getAccountIdentifier())
                .anomalyStatus("Open")
                .build();

        //TODO
//        when(redisUtilities.getAnomalyData(anyString(), anyString())).thenReturn(existingAnomaly);
//        doNothing().when(redisUtilities).updateAnomalyStatus(anyString(), anyString(), any(AnomalyStatus.class), anyString());
        doNothing().when(openSearchRepo).updateAnomalyStatus(anyString(), anyString(), anyString(), anyLong(), anyString());
        doNothing().when(rabbitTemplate).convertAndSend(anyString(), anyString());

        // When
        AnomalyResponse response = anomalyManagementService.closeAnomaly(anomalySummary);

        // Then
        assertTrue(response.isSuccess());
        assertEquals("Close", response.getAnomalyStatus());
    }
}
