package com.heal.event.detector.service;

import com.heal.event.detector.config.RabbitMqConfig;
import com.heal.event.detector.pojos.AnomalyRequest;
import com.heal.event.detector.pojos.AnomalyResponse;
import com.heal.event.detector.repo.RedisUtilities;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ExtendWith(SpringExtension.class)
class AnomalyServiceCreateAnomalyTest {

    @Autowired
    private AnomalyManagementService anomalyService;

    @Autowired
    private RedisUtilities redisUtilities;

    @Autowired
    private RabbitTemplate rabbitTemplate; // or however you're publishing to RabbitMQ

    @Autowired
    private RabbitMqConfig config;

    private final String anomalyCreateQueueName = "anomaly-create";

    @BeforeEach
    void setup() {
    }

    @Test
    void testCreateAnomaly_Success() {
        // Arrange
        AnomalyRequest request = new AnomalyRequest();
        request.setAccountIdentifier("demo");
        request.setEntityId("65b5d4b0-cf19-4233-9b2f-9d45ab6f545f");
        request.setKpiId(2);
        request.setKpiAttribute("64695");
        request.setViolationFor("SOR");
        request.setLastSeverityId(431);
        request.setAnomalyTime(System.currentTimeMillis());
        request.setAnomalyStartTime(System.currentTimeMillis());
        request.setAnomalyEndTime(System.nanoTime());
        request.setLastSeverityId(432);
        request.setStartSeverityId(431);

        // Act
        AnomalyResponse response = anomalyService.createAnomaly(request, false);

        // Assert
        assertNotNull(response);
    }

    @Test
    void testCreateAnomaly_Failure_HandledGracefully() {
        // Arrange
        AnomalyRequest request = new AnomalyRequest();
        request.setAccountIdentifier("acct-456");
        request.setEntityId("entity-2");
        request.setKpiId(888L);
        request.setKpiAttribute("Memory");
        request.setEntityType("APP");
        request.setViolationFor("HIGH_MEM");

        // Act
        AnomalyResponse response = anomalyService.createAnomaly(request, true);

        // Assert
        assertNotNull(response);
        assertFalse(response.isRabbitMqProcessed());
    }
}

