package com.heal.event.detector.service;

import com.heal.event.detector.pojos.AnomalyRequest;
import com.heal.event.detector.pojos.AnomalyResponse;
import com.heal.event.detector.pojos.enums.AnomalyStatus;
import com.heal.event.detector.utility.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;

/**
 * Integration tests for the new Heal Anomaly Management Service
 */
@Slf4j
@SpringBootTest
public class AnomalyManagementServiceIT {

    @Autowired
    private AnomalyManagementService anomalyManagementService;

    @Autowired
    private HealthMetrics metrics;

    @Test
    public void testCreateAnomaly_success() {
        // Create a sample anomaly request
        AnomalyRequest anomalyRequest = createSampleAnomalyRequest();
        
        // Test anomaly creation
        AnomalyResponse response = anomalyManagementService.createAnomaly(anomalyRequest);
        
        // Verify response
        assert response != null;
        assert response.isSuccess();
        assert response.getAnomalyId().equals(anomalyRequest.getAnomalyId());
        assert response.getAccountIdentifier().equals(anomalyRequest.getAccountIdentifier());
        assert response.getStatus() == AnomalyStatus.OPEN;
        assert response.getOperation().equals("CREATE");
        
        // Verify processing flags
        assert response.isRedisProcessed();
        assert response.isOpenSearchProcessed();
        assert response.isRabbitMqProcessed();
        
        log.info("✅ Create anomaly test passed: {}", response.getAnomalyId());
    }

    @Test
    public void testUpdateAnomaly_success() {
        // First create an anomaly
        AnomalyRequest createRequest = createSampleAnomalyRequest();
        AnomalyResponse createResponse = anomalyManagementService.createAnomaly(createRequest);
        assert createResponse.isSuccess();
        
        // Now update it
        AnomalyRequest updateRequest = createRequest.toBuilder()
                .severity("432") // Change severity
                .priority("P1") // Change priority
                .assignedTo("test-user")
                .build();
        
        AnomalyResponse updateResponse = anomalyManagementService.updateAnomaly(updateRequest);
        
        // Verify update response
        assert updateResponse != null;
        assert updateResponse.isSuccess();
        assert updateResponse.getAnomalyId().equals(createRequest.getAnomalyId());
        assert updateResponse.getStatus() == AnomalyStatus.ONGOING;
        assert updateResponse.getOperation().equals("UPDATE");
        
        log.info("✅ Update anomaly test passed: {}", updateResponse.getAnomalyId());
    }

    @Test
    public void testCloseAnomaly_success() {
        // First create an anomaly
        AnomalyRequest createRequest = createSampleAnomalyRequest();
        AnomalyResponse createResponse = anomalyManagementService.createAnomaly(createRequest);
        assert createResponse.isSuccess();
        
        // Now close it
        String reason = "Test closure";
        String closedBy = "test-user";
        AnomalyResponse closeResponse = anomalyManagementService.closeAnomaly(
                createRequest.getAnomalyId(),
                createRequest.getAccountIdentifier(),
                reason,
                closedBy
        );
        
        // Verify close response
        assert closeResponse != null;
        assert closeResponse.isSuccess();
        assert closeResponse.getAnomalyId().equals(createRequest.getAnomalyId());
        assert closeResponse.getStatus() == AnomalyStatus.CLOSED;
        assert closeResponse.getOperation().equals("CLOSE");
        
        log.info("✅ Close anomaly test passed: {}", closeResponse.getAnomalyId());
    }

    @Test
    public void testAnomalyLifecycle_complete() {
        // Test complete lifecycle: Create -> Update -> Close
        
        // 1. Create
        AnomalyRequest createRequest = createSampleAnomalyRequest();
        AnomalyResponse createResponse = anomalyManagementService.createAnomaly(createRequest);
        assert createResponse.isSuccess();
        assert createResponse.getStatus() == AnomalyStatus.OPEN;
        
        // 2. Update
        AnomalyRequest updateRequest = createRequest.toBuilder()
                .severity("433") // Critical
                .assignedTo("ops-team")
                .build();
        AnomalyResponse updateResponse = anomalyManagementService.updateAnomaly(updateRequest);
        assert updateResponse.isSuccess();
        assert updateResponse.getStatus() == AnomalyStatus.ONGOING;
        
        // 3. Close
        AnomalyResponse closeResponse = anomalyManagementService.closeAnomaly(
                createRequest.getAnomalyId(),
                createRequest.getAccountIdentifier(),
                "Issue resolved",
                "ops-team"
        );
        assert closeResponse.isSuccess();
        assert closeResponse.getStatus() == AnomalyStatus.CLOSED;
        
        log.info("✅ Complete lifecycle test passed for anomaly: {}", createRequest.getAnomalyId());
    }

    @Test
    public void testUpdateNonExistentAnomaly_failure() {
        // Try to update an anomaly that doesn't exist
        AnomalyRequest updateRequest = createSampleAnomalyRequest();
        updateRequest.setAnomalyId("NON_EXISTENT_" + RandomStringUtils.random(8, true, true));
        
        AnomalyResponse response = anomalyManagementService.updateAnomaly(updateRequest);
        
        // Should fail gracefully
        assert response != null;
        assert !response.isSuccess();
        assert response.getMessage().contains("not found");
        
        log.info("✅ Update non-existent anomaly test passed - failed gracefully");
    }

    @Test
    public void testCloseNonExistentAnomaly_failure() {
        // Try to close an anomaly that doesn't exist
        String nonExistentId = "NON_EXISTENT_" + RandomStringUtils.random(8, true, true);
        
        AnomalyResponse response = anomalyManagementService.closeAnomaly(
                nonExistentId,
                "test-account",
                "Test reason",
                "test-user"
        );
        
        // Should fail gracefully
        assert response != null;
        assert !response.isSuccess();
        assert response.getMessage().contains("not found");
        
        log.info("✅ Close non-existent anomaly test passed - failed gracefully");
    }

    @Test
    public void testMetricsUpdated() {
        // Record initial metrics
        long initialCreated = metrics.getHealAnomaliesCreated();
        long initialUpdated = metrics.getHealAnomaliesUpdated();
        long initialClosed = metrics.getHealAnomaliesClosed();
        
        // Perform operations
        AnomalyRequest request = createSampleAnomalyRequest();
        
        // Create
        anomalyManagementService.createAnomaly(request);
        assert metrics.getHealAnomaliesCreated() > initialCreated;
        
        // Update
        anomalyManagementService.updateAnomaly(request);
        assert metrics.getHealAnomaliesUpdated() > initialUpdated;
        
        // Close
        anomalyManagementService.closeAnomaly(request.getAnomalyId(), request.getAccountIdentifier(), "test", "test");
        assert metrics.getHealAnomaliesClosed() > initialClosed;
        
        log.info("✅ Metrics update test passed");
    }

    /**
     * Helper method to create a sample anomaly request for testing
     */
    private AnomalyRequest createSampleAnomalyRequest() {
        String randomId = RandomStringUtils.random(8, true, true);
        
        Map<String, String> thresholds = new HashMap<>();
        thresholds.put("Upper", "90.0");
        thresholds.put("Lower", "10.0");
        
        Map<String, String> metadata = new HashMap<>();
        metadata.put("source", "integration-test");
        metadata.put("testId", randomId);
        
        Set<String> serviceIds = new HashSet<>();
        serviceIds.add("test-service-1");
        serviceIds.add("test-service-2");
        
        return AnomalyRequest.builder()
                .anomalyId("TEST_ANOMALY_" + System.currentTimeMillis() + "_" + randomId)
                .accountIdentifier("test-account-" + randomId)
                .instanceId("test-instance-" + randomId)
                .kpiId(24L)
                .kpiAttribute("CPU_UTILIZATION")
                .categoryId("PERFORMANCE")
                .serviceIds(serviceIds)
                .thresholdType("Static")
                .operationType("greater than")
                .value(85.5)
                .thresholds(thresholds)
                .metadata(metadata)
                .status(AnomalyStatus.OPEN)
                .anomalyTime(System.currentTimeMillis())
                .identifiedTime(System.currentTimeMillis())
                .kpiIdentifier("CPU_UTIL_KPI")
                .severity("432") // Warning
                .assignedTo("system")
                .priority("P2")
                .build();
    }
}
