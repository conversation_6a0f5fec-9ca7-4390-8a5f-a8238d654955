# ==========================================================
# RabbitMQ server configuration
# Basic configuration needed to communicate with RabbitMQ.
# HTTP mode of communication will be utilized in this case.
# ==========================================================
spring.rabbitmq.addresses=rabbitmq.appnomic:5672
spring.rabbitmq.username=guest
spring.rabbitmq.password=
spring.rabbitmq.ssl.enabled=true
spring.rabbitmq.ssl.algorithm=TLSv1.3
spring.rabbitmq.aggregatedKpiInputQueueName=integration_test_static-aggregated-kpi
spring.rabbitmq.violatedEventInputQueueName=integration_test_violated-events
spring.rabbitmq.anomalyOutputSignalQueueName=integration_test_anomaly-event-signal-messages
spring.rabbitmq.anomalyOutputActionQueueName=integration_test_anomaly-event-action-messages
spring.rabbitmq.norThresholdQueueName=integration_test_nor-thresholds

# ==========================================================
#Redis Server Configuration
# ==========================================================
spring.redis.cluster.nodes=**************:9001,**************:9002,**************:9001,**************:9002,**************:9001,**************:9002
spring.redis.ssl=true

# ==========================================================
# Miscellaneous
# opensearch.data.push.schedule.initial.delay in secs
# opensearch.data.push.schedule.interval in secs
# ==========================================================
opensearch.data.push.schedule.initial.delay=1000000
opensearch.data.push.schedule.interval=1000000

# ==========================================================
# OpenSearch Details
# ==========================================================
opensearch.nodes=opensearch-node1.appnomic:9200
opensearch.username=admin
opensearch.password=
opensearch.protocol=http
opensearch.kpi.violations.index=integration_test_ed_kpi_violations
opensearch.transaction.violations.index=integration_test_ed_transaction_violations
opensearch.batchjob.violations.index=integration_test_ed_batch_job_violations
opensearch.anomalies.index=integration_test_ed_anomalies
opensearch.kpi.thresholds.index=integration_test_ed_kpis_thresholds
opensearch.transaction.thresholds.index=integration_test_ed_transactions_thresholds