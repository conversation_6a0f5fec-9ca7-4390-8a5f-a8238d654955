package com.heal.event.detector.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

@Data
@AllArgsConstructor
@Builder
public class DelayThresholdQueue implements Delayed {

    private final String key;
    private final String type;
    private final long timeoutDuration;

    @Override
    public long getDelay(TimeUnit unit) {
        long diff = timeoutDuration - System.currentTimeMillis();
        return unit.convert(diff, TimeUnit.MILLISECONDS);
    }

    @Override
    public int compareTo(@NonNull Delayed requestData) {
        return Long.compare(this.timeoutDuration, ((DelayThresholdQueue) requestData).timeoutDuration);
    }

    @Override
    public String toString() {
        return "DelayRequestElement{key=" + key + ", type=" + type + ", expiryTime=" + timeoutDuration + "}";
    }
}
