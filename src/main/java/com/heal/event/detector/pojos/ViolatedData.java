package com.heal.event.detector.pojos;

import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.heal.event.detector.pojos.enums.ViolationEventType;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class ViolatedData implements Serializable {

    private static final long serialVersionUID = 1L;

    private String anomalyId;
    private String accountId;
    private List<String> appIds;
    private List<String> serviceList;
    private int timezoneOffsetInSeconds;
    private ViolationEventType eventType;
    private String thresholdType;
    private String operationType;
    private long violationTime; //Epoch time in milli sec
    private String category;
    private String kpiId;
    private int isInfo;

    private String agentIdentifier;

    //for kpi
    private String instanceId;
    private String kpiAttribute;
    private String displayAttributeName;

    private boolean isMaintenanceExcluded;

    //for txn
    private String transactionId;
    private String responseTimeType; //dc, eue

    private Map<String, Double> thresholds;
    private String value;
    private String thresholdSeverity;

    private long violationStartTimeInMinute; //Epoch time in minute - for processing
    private String kpiViolationTime;
    private int persistence;
    private int suppression;
    private String violationFor;//NOR or SOR
    private KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType kpiType;

    //Batch job
    private String batchJob;
    private Map<String, String> metaData = new HashMap<>();

    public ViolatedData(String accountId, List<String> appIds) {
        this.accountId = accountId;
        this.appIds = appIds;
    }

    public ViolatedData(ViolatedData other) {
        this.anomalyId = other.anomalyId;
        this.accountId = other.accountId;
        this.appIds = other.appIds != null ? new java.util.ArrayList<>(other.appIds) : null;
        this.serviceList = other.serviceList != null ? new java.util.ArrayList<>(other.serviceList) : null;
        this.timezoneOffsetInSeconds = other.timezoneOffsetInSeconds;
        this.eventType = other.eventType;
        this.thresholdType = other.thresholdType;
        this.operationType = other.operationType;
        this.violationTime = other.violationTime;
        this.category = other.category;
        this.kpiId = other.kpiId;
        this.isInfo = other.isInfo;
        this.agentIdentifier = other.agentIdentifier;
        this.instanceId = other.instanceId;
        this.kpiAttribute = other.kpiAttribute;
        this.displayAttributeName = other.displayAttributeName;
        this.isMaintenanceExcluded = other.isMaintenanceExcluded;
        this.transactionId = other.transactionId;
        this.responseTimeType = other.responseTimeType;
        this.thresholds = other.thresholds != null ? new java.util.HashMap<>(other.thresholds) : null;
        this.value = other.value;
        this.thresholdSeverity = other.thresholdSeverity;
        this.violationStartTimeInMinute = other.violationStartTimeInMinute;
        this.kpiViolationTime = other.kpiViolationTime;
        this.persistence = other.persistence;
        this.suppression = other.suppression;
        this.violationFor = other.violationFor;
        this.kpiType = other.kpiType;
        this.batchJob = other.batchJob;
        this.metaData = other.metaData != null ? new java.util.HashMap<>(other.metaData) : new java.util.HashMap<>();
    }

    public Map<String, String> fetchMetaData() {
        Map<String, String> metaData = new HashMap<>(getMetaData());
        if (thresholds.containsKey("anomalyScore")) {
            metaData.put("anomalyScore", String.valueOf(thresholds.get("anomalyScore")));
        }
        if (kpiAttribute != null && displayAttributeName != null) {
            metaData.put("displayAttributeName", displayAttributeName);
        }
        if (this.metaData.containsKey("fileName")) {
            metaData.put("fileName", this.metaData.get("fileName"));
        }
        metaData.put("isInformatic", String.valueOf(getIsInfo()));
        metaData.put("isMaintenanceExcluded", isMaintenanceExcluded() ? "1" : "0");
        return metaData;
    }

    public Map<String, String> getMetaData() {
        return metaData;
    }

}
