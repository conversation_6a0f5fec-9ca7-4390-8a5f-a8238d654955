package com.heal.event.detector.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.opensearch.client.opensearch.core.UpdateByQueryRequest;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UpdateByQueryRequestInsertDetails {
    private UpdateByQueryRequest updateByQueryRequest;
    private String accountIdentifier;
    private String indexName;
}
