package com.heal.event.detector.pojos.enums;

import java.io.Serializable;

public enum ThresholdType implements Serializable {
    BASELINE("Baseline"),
    REALTIME("Realtime"),
    STATIC("Static");

    private String thresholdType;

    ThresholdType(String thresholdType) {
        this.thresholdType = thresholdType;
    }

    public String getThresholdType() {
        return thresholdType;
    }
}
