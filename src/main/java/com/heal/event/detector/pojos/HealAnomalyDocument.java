package com.heal.event.detector.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Document structure for heal_anomalies_* OpenSearch index
 * Represents the main anomaly document that gets updated throughout the open to close cycle
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HealAnomalyDocument {
    
    private String anomalyId;
    private Long anomalyStartTime; // first violation time
    private Long anomalyEndTime; // last violation time
    private Long anomalyTime; // anomaly created time (persistence meet time)
    private String anomalyStatus; // Open/Ongoing/Close
    private Long identifiedTime; // anomaly created time (Event detector identified time)
    private String entityType; // Transaction/ComponentInstance/ApplicationInstance/ServiceInstance/Journey
    private String entityId; // Based on entity type, entity identifier will be populated
    private int lastSeverityId; // 431 - Low, 432 - Warn, 433 - Critical
    private int startSeverityId;
    private String categoryId;
    private String kpiAttribute;
    private Long kpiId;
    private String kpiIdentifier;
    private List<String> serviceId;
    private Map<String, Object> metadata;
    private String operationType;
    private List<String> signalIds;
    private String thresholdType;
    private Map<String, Double> lastThresholdsMeet;
    private String anomalyScore;
    private String value;
    private String closingReason; // Violations are stopped/Maintenance window/Data collection stopped
    private Date timestamp; // @timestamp field for OpenSearch

    /**
     * Creates a new HealAnomalyDocument from AnomalyRequest
     */
    public static HealAnomalyDocument fromAnomalyRequest(AnomalyRequest request) {
        return HealAnomalyDocument.builder()
                .anomalyId(request.getAnomalyId())
                .anomalyStartTime(request.getAnomalyTime())
                .anomalyEndTime(request.getAnomalyTime())
                .anomalyTime(request.getAnomalyTime())
                .anomalyStatus(mapStatusToHealFormat(request.getAnomalyStatus() != null ? request.getAnomalyStatus() : "Open"))
                .identifiedTime(request.getIdentifiedTime())
                .entityType(request.getEntityType())
                .entityId(request.getEntityId())
                .lastSeverityId(request.getLastSeverityId())
                .startSeverityId(request.getStartSeverityId())
                .categoryId(request.getCategoryId())
                .kpiAttribute(request.getKpiAttribute())
                .kpiId(request.getKpiId())
                .kpiIdentifier(request.getKpiIdentifier())
                .serviceId(request.getServiceId() != null ? List.copyOf(request.getServiceId()) : null)
                .metadata(convertMetadata(request.getMetadata(), request.getMetadata()))
                .operationType(request.getOperationType())
                .thresholdType(request.getThresholdType())
                .lastThresholdsMeet(convertThresholds(request.getLastThresholdsMeet()))
                .anomalyScore(request.getAnomalyScore())
                .value(request.getValue())
                .build();
    }
    
    private static String mapStatusToHealFormat(String status) {
        if (status == null) return "Open";
        switch (status.toUpperCase()) {
            case "OPEN": return "Open";
            case "UPDATED": return "Ongoing";
            case "CLOSED": return "Close";
            default: return "Open";
        }
    }
    
    private static Map<String, Object> convertMetadata(Map<String, String> metadata, Map<String, String> additionalData) {
        Map<String, Object> result = new java.util.HashMap<>();
        
        // Add default metadata fields
        result.put("isMaintenanceExcluded", "0");
        result.put("violationLevel", "SERVICE");
        result.put("isInformatic", "0");
        result.put("kpiType", "Core");
        result.put("remaindersCount", 0);
        result.put("closeWindowResetCount", 0);
        result.put("databreakResetCount", 0);
        result.put("lowPersistenceMeetCount", 0);
        result.put("lowSuppressionMeetCount", 0);
        result.put("mediumPersistenceMeetCount", 0);
        result.put("mediumSuppressionMeetCount", 0);
        result.put("highPersistenceMeetCount", 0);
        result.put("highSuppressionMeetCount", 0);
        
        // Add metadata from request
        if (metadata != null) {
            result.putAll(metadata);
        }
        
        // Add additional data
        if (additionalData != null) {
            result.putAll(additionalData);
        }
        
        return result;
    }
    
    private static Map<String, Double> convertThresholds(Map<String, Double> thresholds) {
        if (thresholds == null) return null;
        
        Map<String, Double> result = new java.util.HashMap<>();
        thresholds.forEach((key, value) -> {
            try {
                result.put(key, value);
            } catch (NumberFormatException e) {
                result.put(key, 0.0);
            }
        });
        return result;
    }
}
