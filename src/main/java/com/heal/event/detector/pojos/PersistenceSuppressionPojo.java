package com.heal.event.detector.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PersistenceSuppressionPojo implements Serializable {

    private long lastViolationTime;
    private int violationCount;
    private long violationStartTime;
    private int persistence;
    private int suppression;

}
