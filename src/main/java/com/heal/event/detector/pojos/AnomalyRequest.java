package com.heal.event.detector.pojos;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Set;

/**
 * Request object for anomaly management operations
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AnomalyRequest {
    
    private String accountIdentifier;
    private String anomalyId;
    private long anomalyStartTime;
    private long anomalyEndTime;
    private long anomalyTime;
    private String anomalyStatus;
    private String categoryId;
    private long identifiedTime;
    private String entityType;
    private String entityId;
    private int lastSeverityId;
    private int startSeverityId;
    private String kpiAttribute;
    private long kpiId;
    private Map<String, String> metadata;
    private String operationType;
    private Set<String> signalIds;
    private String thresholdType;
    private Map<String, Double> lastThresholdsMeet;
    private String kpiIdentifier;
    private String violationFor;

    @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
    @JsonDeserialize(as = Set.class, contentAs = String.class)
    private Set<String> serviceId;
    private String anomalyScore;
    private String value;
    private String closingReason;
    @JsonProperty("@timestamp")
    private String timestamp;
}
