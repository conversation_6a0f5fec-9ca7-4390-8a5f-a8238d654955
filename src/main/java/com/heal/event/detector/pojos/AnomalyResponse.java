package com.heal.event.detector.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * Response object for anomaly management operations
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AnomalyResponse {
    
    private String anomalyId;
    private String accountIdentifier;
    private String anomalyStatus;
    private String operation;
    private boolean success;
    private String message;
    private Long timestamp;
    private Map<String, Object> data;
    
    // Processing status for each system
    private boolean rabbitMqProcessed;
    private boolean redisProcessed;
    private boolean openSearchProcessed;
    
    // Error details if any
    private String errorCode;
    private String errorDetails;
    
    public static AnomalyResponse success(String anomalyId, String accountIdentifier, 
                                       String anomalyStatus, String operation) {
        return AnomalyResponse.builder()
                .anomalyId(anomalyId)
                .accountIdentifier(accountIdentifier)
                .anomalyStatus(anomalyStatus)
                .operation(operation)
                .success(true)
                .timestamp(System.currentTimeMillis())
                .message("Operation completed successfully")
                .build();
    }
    
    public static AnomalyResponse failure(String anomalyId, String accountIdentifier, 
                                        String operation, String errorMessage) {
        return AnomalyResponse.builder()
                .anomalyId(anomalyId)
                .accountIdentifier(accountIdentifier)
                .operation(operation)
                .success(false)
                .timestamp(System.currentTimeMillis())
                .message(errorMessage)
                .build();
    }
}
