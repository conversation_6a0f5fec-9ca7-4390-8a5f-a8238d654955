package com.heal.event.detector.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response object for anomaly management operations
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AnomalyResponse {
    
    private String anomalyId;
    private String accountIdentifier;
    private String anomalyStatus;
    private boolean success;

    // Processing status for each system
    private boolean rabbitMqProcessed;
    private boolean redisProcessed;
    private boolean openSearchProcessed;
    
    public static AnomalyResponse success(String anomalyId, String accountIdentifier,
                                       String anomalyStatus) {
        return AnomalyResponse.builder()
                .anomalyId(anomalyId)
                .accountIdentifier(accountIdentifier)
                .anomalyStatus(anomalyStatus)
                .success(true)
                .build();
    }
    
    public static AnomalyResponse failure(String anomalyId, String accountIdentifier,
                                          String anomalyStatus) {
        return AnomalyResponse.builder()
                .anomalyId(anomalyId)
                .accountIdentifier(accountIdentifier)
                .anomalyStatus(anomalyStatus)
                .success(false)
                .build();
    }
}
