package com.heal.event.detector.service;

import com.appnomic.appsone.common.protbuf.AggregatedKpiProtos;
import com.appnomic.appsone.common.protbuf.ThresholdProtos;
import com.appnomic.appsone.common.protbuf.ViolatedEventProtos;
import com.heal.configuration.protbuf.AnomalySummaryProtos;
import com.heal.configuration.protbuf.ThresholdsClosedProtos;
import com.heal.event.detector.config.RabbitMqConfig;
import com.heal.event.detector.core.ProtoValidation;
import com.heal.event.detector.pojos.AnomalyRequest;
import com.heal.event.detector.pojos.AnomalyResponse;
import com.heal.event.detector.utility.HealthMetrics;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;

@Slf4j
@Service
public class EventReceiverFromQueue {

    @Autowired
    private ProtoValidation protoValidation;

    @Autowired
    RabbitMqConfig rabbitMqConfig;

    @Autowired
    private HealthMetrics metrics;

    @Autowired
    private AnomalyManagementService anomalyManagementService;

    @Autowired
    private ObjectMapper objectMapper;

    public void receiveAggregatedKPIsData(byte[] aggregatedKpiStream) {

        metrics.updateReadCountAggregatedKpiQueue();
        metrics.updateSnapshots(rabbitMqConfig.aggregatedKpiInputQueueName, 1);

        try {
            AggregatedKpiProtos.AggregatedKpi aggregatedKpi;
            try {
                aggregatedKpi = AggregatedKpiProtos.AggregatedKpi.parseDelimitedFrom(new ByteArrayInputStream(aggregatedKpiStream));
            } catch (IOException e) {
                aggregatedKpi = AggregatedKpiProtos.AggregatedKpi.parseFrom(aggregatedKpiStream);
            }

            log.trace("AggregatedKpi data received. Details: {}", aggregatedKpi);

            protoValidation.validateAndProcessInputAggregatedKPI(aggregatedKpi);
        } catch (Exception e) {
            metrics.updateRmqReadErrors();
            log.error("Exception in receiving event from {} queue. ", rabbitMqConfig.aggregatedKpiInputQueueName, e);
        }
    }

    public void receiveViolatedEventsData(byte[] violatedEventStream) {

        metrics.updateReadCountViolatedEventQueue();
        metrics.updateSnapshots(rabbitMqConfig.violatedEventInputQueueName, 1);

        try {
            ViolatedEventProtos.ViolatedEvent violatedEvent = ViolatedEventProtos.ViolatedEvent.parseFrom(violatedEventStream);

            log.trace("Violated event received. Details: {}", violatedEvent);

            protoValidation.validateAndProcessInputViolatedEvents(violatedEvent);
        } catch (Exception e) {
            metrics.updateRmqReadErrors();
            log.error("Exception in receiving event from {} queue. ", rabbitMqConfig.violatedEventInputQueueName, e);
        }
    }

    public void receiveNorThresholdData(byte[] norThresholdsStream) {

        metrics.updateReadCountNorThresholdQueue();
        metrics.updateSnapshots(rabbitMqConfig.norThresholdQueueName, 1);

        try {
            ThresholdProtos.Threshold threshold = ThresholdProtos.Threshold.parseFrom(norThresholdsStream);

            log.trace("Threshold data received. Details: {}", threshold);

            protoValidation.validateAndProcessInputNorThresholdEvents(threshold);
        } catch (Exception e) {
            metrics.updateRmqReadErrors();
            log.error("Exception in receiving event from {} queue. ", rabbitMqConfig.norThresholdQueueName, e);
        }
    }

    public void receiveNorClosedThresholdData(byte[] norClosedThresholdsStream) {

        metrics.updateReadCountNorClosedThresholdQueue();
        metrics.updateSnapshots(rabbitMqConfig.norClosedThresholdQueueName, 1);

        try {
            ThresholdsClosedProtos.NORThresholdsClosed norThresholdsClosed = ThresholdsClosedProtos.NORThresholdsClosed.parseFrom(norClosedThresholdsStream);

            log.trace("Nor Closed Threshold data received. Details: {}", norThresholdsClosed);

            protoValidation.validateAndProcessInputNorClosedThresholdEvents(norThresholdsClosed);
        } catch (Exception e) {
            metrics.updateRmqReadErrors();
            log.error("Exception in receiving event from {} queue. ", rabbitMqConfig.norClosedThresholdQueueName, e);
        }
    }

    /**
     * Receive anomaly creation events from anomaly-create queue
     */
    public void receiveAnomalyCreateData(byte[] anomalyCreateStream) {
        metrics.updateSnapshots(rabbitMqConfig.anomalyCreateQueueName, 1);

        try {
            String jsonData = new String(anomalyCreateStream);
            AnomalyRequest anomalyRequest = objectMapper.readValue(jsonData, AnomalyRequest.class);

            log.trace("Anomaly create data received. Details: {}", anomalyRequest);

            AnomalyResponse response = anomalyManagementService.createAnomaly(anomalyRequest, false);
            if (!response.isSuccess()) {
                log.error("Failed to process anomaly create request: {}", response);
                metrics.updateRmqReadErrors();
            }
        } catch (Exception e) {
            metrics.updateRmqReadErrors();
            log.error("Exception in receiving event from {} queue. ", rabbitMqConfig.anomalyCreateQueueName, e);
        }
    }

    /**
     * Receive anomaly update events from anomaly-update queue
     */
    public void receiveAnomalyUpdateData(byte[] anomalyUpdateStream) {
        metrics.updateSnapshots(rabbitMqConfig.anomalyUpdateQueueName, 1);

        try {
            String jsonData = new String(anomalyUpdateStream);
            AnomalyRequest anomalyRequest = objectMapper.readValue(jsonData, AnomalyRequest.class);

            log.trace("Anomaly update data received. Details: {}", anomalyRequest);

            AnomalyResponse response = anomalyManagementService.updateAnomaly(anomalyRequest);
            if (!response.isSuccess()) {
                log.error("Failed to process anomaly update request: {}", response);
                metrics.updateRmqReadErrors();
            }
        } catch (Exception e) {
            metrics.updateRmqReadErrors();
            log.error("Exception in receiving event from {} queue. ", rabbitMqConfig.anomalyUpdateQueueName, e);
        }
    }

    /**
     * Receive anomaly close events from anomaly-close queue
     */
    public void receiveAnomalyCloseData(byte[] anomalyCloseStream) {
        metrics.updateReadCountReceiveAnomalyCloseQueue();
        metrics.updateSnapshots(rabbitMqConfig.anomalyCloseQueueName, 1);

        try {
            String jsonData = new String(anomalyCloseStream);
            // Expecting JSON with anomalyId, accountIdentifier, reason, closedBy
            AnomalySummaryProtos.AnomalySummary anomalySummary = AnomalySummaryProtos.AnomalySummary.parseFrom(anomalyCloseStream);

            log.trace("Anomaly close data received. AnomalyId: {}, Account: {}", anomalySummary.getAnomalyId(), anomalySummary.getAccountIdentifier());

            AnomalyResponse response = anomalyManagementService.closeAnomaly(anomalySummary);
            if (!response.isSuccess()) {
                log.error("Failed to process anomaly close request: {}", response);
                metrics.updateRmqReadErrors();
            }
        } catch (Exception e) {
            metrics.updateRmqReadErrors();
            log.error("Exception in receiving event from {} queue. ", rabbitMqConfig.anomalyCloseQueueName, e);
        }
    }
}