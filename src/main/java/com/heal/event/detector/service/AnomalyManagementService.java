package com.heal.event.detector.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.*;
import com.heal.configuration.protbuf.AnomalySummaryProtos;
import com.heal.event.detector.pojos.AnomalyRequest;
import com.heal.event.detector.pojos.AnomalyResponse;
import com.heal.event.detector.pojos.HealAnomalyDocument;
import com.heal.event.detector.repo.OpenSearchRepo;
import com.heal.event.detector.repo.RedisUtilities;
import com.heal.event.detector.utility.Constants;
import com.heal.event.detector.utility.HealthMetrics;
import com.heal.event.detector.utility.StringUtils;
import com.heal.event.detector.utility.cache.CacheWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Service for managing anomaly lifecycle operations
 * Provides methods to create, update, and close anomalies across RabbitMQ, Redis, and OpenSearch
 */
@Slf4j
@Service
public class AnomalyManagementService {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private RedisUtilities redisUtilities;

    @Autowired
    private OpenSearchRepo openSearchRepo;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private HealthMetrics metrics;

    @Value("${spring.rabbitmq.anomalyCreateQueueName:anomaly-create}")
    private String anomalyCreateQueueName;

    @Value("${spring.rabbitmq.anomalyUpdateQueueName:anomaly-update}")
    private String anomalyUpdateQueueName;

    @Value("${spring.rabbitmq.anomalyCloseQueueName:anomaly-close}")
    private String anomalyCloseQueueName;

    @Autowired
    CacheWrapper cacheWrapper;

    private static final String AE_PREFIX = "AE";
    private static final String AE_SPLITTER = "-";

    /**
     * Creates a new anomaly and publishes to RabbitMQ, stores in Redis, and indexes in OpenSearch
     *
     * @param anomalyRequest The anomaly data to create
     * @return AnomalyResponse indicating success/failure and processing status
     */
    public AnomalyResponse createAnomaly(AnomalyRequest anomalyRequest, boolean isTxn) {
        log.info("Creating anomaly for account: {}, anomalyId: {}",
                anomalyRequest.getAccountIdentifier(), anomalyRequest.getAnomalyId());

        // Generate anomaly ID if not provided
        if (anomalyRequest.getAnomalyId() == null || anomalyRequest.getAnomalyId().isEmpty()) {
            String anomalyEvent = getAnomalyIdentifier(anomalyRequest, isTxn);
            anomalyRequest.setAnomalyId(anomalyEvent);
        }

        AnomalyResponse response = AnomalyResponse.success(
                anomalyRequest.getAnomalyId(),
                anomalyRequest.getAccountIdentifier(),
                anomalyRequest.getAnomalyStatus(),
                Constants.ANOMALY_OPERATION_CREATE
        );

        try {
            // 1: Retrieve existing violation details from Redis and update it with the new anomaly information
            ViolationDetails violationDetails = redisUtilities.getViolationDetails(anomalyRequest.getAccountIdentifier(), anomalyRequest.getEntityId(), String.valueOf(anomalyRequest.getKpiId()), anomalyRequest.getKpiAttribute(), anomalyRequest.getViolationFor(), anomalyRequest.getEntityType());
            if (violationDetails != null) {
                AnomalyEventStatus anomalyEventStatus = violationDetails.getAnomalyEventStatus();
                if (anomalyEventStatus == null) {
                    anomalyEventStatus = new AnomalyEventStatus();
                    anomalyEventStatus.setAnomalyEventId(anomalyRequest.getAnomalyId());
                    anomalyEventStatus.setAnomalyStatus("Open");
                    anomalyEventStatus.setAnomalyStartTime(System.currentTimeMillis());
                    anomalyEventStatus.setLastAnomalySeverity(String.valueOf(anomalyRequest.getLastSeverityId()));
                }
                violationDetails.setAnomalyEventStatus(anomalyEventStatus);
                redisUtilities.putViolationDetails(anomalyRequest.getAccountIdentifier(), anomalyRequest.getEntityId(), String.valueOf(anomalyRequest.getKpiId()), anomalyRequest.getKpiAttribute(), anomalyRequest.getViolationFor(), anomalyRequest.getEntityType(), violationDetails);
                log.debug("Anomaly stored in Redis successfully");
            } else {
                log.debug("Violations details is null");
                return AnomalyResponse.failure(null,null,null,"Violations details is null");
            }

            // 2. Index in OpenSearch (heal_anomalies_* index)
            openSearchRepo.insertOrUpdateAnomalyData(
                    anomalyRequest.getAccountIdentifier(),
                    anomalyRequest
            );

            // 3. Publish to RabbitMQ
            publishToRabbitMQ(anomalyCreateQueueName, anomalyRequest, "CREATE");
            response.setRabbitMqProcessed(true);
            log.debug("Anomaly published to RabbitMQ successfully");

            // Update metrics
            metrics.updateSnapshots(anomalyCreateQueueName, 1);

            log.info("Anomaly created successfully: {}", anomalyRequest.getAnomalyId());
            return response;

        } catch (Exception e) {
            log.error("Error creating anomaly: {}", anomalyRequest.getAnomalyId(), e);
            return AnomalyResponse.failure(
                    anomalyRequest.getAnomalyId(),
                    anomalyRequest.getAccountIdentifier(),
                    Constants.ANOMALY_OPERATION_CREATE,
                    "Failed to create anomaly: " + e.getMessage()
            );
        }
    }

    public String getAnomalyIdentifier(AnomalyRequest anomalyRequest, boolean isTxn) {
        String accountId = anomalyRequest.getAccountIdentifier();
        String instId = anomalyRequest.getEntityId();

        Account accountData = cacheWrapper.getAccountDetails(accountId);

        String anomalyEvent = null;

        if (isTxn) {
            Transaction txnData = cacheWrapper.getTransactionDetails(accountId, anomalyRequest.getEntityId());
            if (Objects.nonNull(accountData) && Objects.nonNull(txnData)) {
                anomalyEvent = AE_PREFIX + AE_SPLITTER + accountData.getId() +
                        AE_SPLITTER +
                        txnData.getId() +
                        AE_SPLITTER +
                        anomalyRequest.getKpiId() +
                        AE_SPLITTER +
                        "T" +
                        AE_SPLITTER +
                        (anomalyRequest.getViolationFor().equals("SOR") ? "S" : "N") +
                        AE_SPLITTER +
                        TimeUnit.MILLISECONDS.toMinutes(anomalyRequest.getAnomalyStartTime());
            }
        } else {
            boolean isGroupKpi = anomalyRequest.getMetadata() != null && Boolean.parseBoolean(anomalyRequest.getMetadata().getOrDefault("isGroupKpi", (StringUtils.isEmpty(anomalyRequest.getKpiAttribute())
                    && !anomalyRequest.getKpiAttribute().trim().equals(Constants.COMMON_ATTRIBUTE)) ? "true" : "false"));

            CompInstClusterDetails instData = cacheWrapper.getInstanceDetails(accountId, instId);
            if (Objects.nonNull(accountData) && Objects.nonNull(instData)) {
                anomalyEvent = AE_PREFIX + AE_SPLITTER + accountData.getId() +
                        AE_SPLITTER +
                        instData.getId() +
                        AE_SPLITTER +
                        anomalyRequest.getKpiId() +
                        AE_SPLITTER +
                        "C" +
                        AE_SPLITTER +
                        (anomalyRequest.getViolationFor().equals("SOR") ? "S" : "N") +
                        AE_SPLITTER +
                        (!StringUtils.isEmpty(anomalyRequest.getKpiAttribute()) ? (isGroupKpi ? String.valueOf(anomalyRequest.getKpiAttribute().trim().hashCode()) : anomalyRequest.getKpiAttribute().trim()).concat(AE_SPLITTER) : "") +
                        TimeUnit.MILLISECONDS.toMinutes(anomalyRequest.getAnomalyStartTime());
            }
        }

        if (anomalyEvent == null) {
            String anomalyIdPrefix = anomalyRequest.getViolationFor().equals("SOR") ? "AE-S-" : "AE-N-";
            anomalyEvent = anomalyIdPrefix + UUID.randomUUID();
        }

        return anomalyEvent;
    }

    /**
     * Updates an existing anomaly across RabbitMQ, Redis, and OpenSearch
     *
     * @param anomalyRequest The updated anomaly data
     * @return AnomalyResponse indicating success/failure and processing status
     */
    public AnomalyResponse updateAnomaly(AnomalyRequest anomalyRequest) {
        log.info("Updating anomaly for account: {}, anomalyId: {}",
                anomalyRequest.getAccountIdentifier(), anomalyRequest.getAnomalyId());

        if (anomalyRequest.getAnomalyId() == null || anomalyRequest.getAnomalyId().isEmpty()) {
            return AnomalyResponse.failure(
                    null,
                    anomalyRequest.getAccountIdentifier(),
                    Constants.ANOMALY_OPERATION_UPDATE,
                    "Anomaly ID is required for update operation"
            );
        }

        AnomalyResponse response = AnomalyResponse.success(
                anomalyRequest.getAnomalyId(),
                anomalyRequest.getAccountIdentifier(),
                anomalyRequest.getAnomalyStatus(),
                Constants.ANOMALY_OPERATION_UPDATE
        );

        try {
            // 1. Read existing anomaly from Redis
            ViolationDetails violationDetails = redisUtilities.getViolationDetails(anomalyRequest.getAccountIdentifier(), anomalyRequest.getEntityId(), String.valueOf(anomalyRequest.getKpiId()), anomalyRequest.getKpiAttribute(), anomalyRequest.getViolationFor(), anomalyRequest.getEntityType());

            // 2. Update in Redis
            if (violationDetails != null) {
                AnomalyEventStatus anomalyEventStatus = violationDetails.getAnomalyEventStatus();
                anomalyEventStatus.setAnomalyStatus("Ongoing");
                anomalyEventStatus.setAnomalyStartTime(anomalyRequest.getAnomalyStartTime());
                violationDetails.setAnomalyEventStatus(anomalyEventStatus);
                redisUtilities.putViolationDetails(anomalyRequest.getAccountIdentifier(), anomalyRequest.getEntityId(), String.valueOf(anomalyRequest.getKpiId()), anomalyRequest.getKpiAttribute(), anomalyRequest.getViolationFor(), anomalyRequest.getEntityType(), violationDetails);
                log.debug("Anomaly updated in Redis successfully");
            } else {
                log.debug("Violations details is null");
                return AnomalyResponse.failure(null,null,null,"Violations details is null");
            }

            // 3 Read from OpenSearch heal_anomalies_* index as fallback
            AnomalyRequest existingAnomaly = null;

                var healAnomalyDoc = openSearchRepo.getHealAnomalyFromOpenSearch(
                        anomalyRequest.getAccountIdentifier(),
                        anomalyRequest.getAnomalyId()
                );
                if (healAnomalyDoc != null) {
                    existingAnomaly = convertHealAnomalyToRequest(healAnomalyDoc, anomalyRequest.getAccountIdentifier());
                }

            if (existingAnomaly == null) {
                return AnomalyResponse.failure(
                        anomalyRequest.getAnomalyId(),
                        anomalyRequest.getAccountIdentifier(),
                        "Open",
                        "Anomaly not found for update"
                );
            }

            // 4. Merge updates with existing data
            AnomalyRequest updatedAnomaly = mergeAnomalyData(existingAnomaly, anomalyRequest);
            updatedAnomaly.setAnomalyStatus("Ongoing");

            // 5. Update in OpenSearch (heal_anomalies_* index)
            openSearchRepo.insertOrUpdateAnomalyData(
                    updatedAnomaly.getAccountIdentifier(),
                    updatedAnomaly
            );
            log.debug("Anomaly updated in heal_anomalies successfully");

            // 6. Publish update to RabbitMQ
            publishToRabbitMQ(anomalyUpdateQueueName, updatedAnomaly, "UPDATE");
            response.setRabbitMqProcessed(true);
            log.debug("Anomaly update published to RabbitMQ successfully");

            // Update metrics
            metrics.updateSnapshots(anomalyUpdateQueueName, 1);

            log.info("Anomaly updated successfully: {}", updatedAnomaly.getAnomalyId());
            return response;

        } catch (Exception e) {
            log.error("Error updating anomaly: {}", anomalyRequest.getAnomalyId(), e);
            return AnomalyResponse.failure(
                    anomalyRequest.getAnomalyId(),
                    anomalyRequest.getAccountIdentifier(),
                    Constants.ANOMALY_OPERATION_UPDATE,
                    "Failed to update anomaly: " + e.getMessage()
            );
        }
    }

    /**
     * Closes an existing anomaly across RabbitMQ, Redis, and OpenSearch
     *
     * @return AnomalyResponse indicating success/failure and processing status
     */
    public AnomalyResponse closeAnomaly(AnomalySummaryProtos.AnomalySummary anomalySummary) {
        log.info("Closing anomaly for account: {}, anomalyId: {}", anomalySummary.getAccountIdentifier(), anomalySummary.getAnomalyId());

        if (anomalySummary.getAnomalyId() == null || anomalySummary.getAnomalyId().isEmpty()) {
            return AnomalyResponse.failure(
                    null,
                    anomalySummary.getAccountIdentifier(),
                    anomalySummary.getAnomalyStatus(),
                    "Anomaly ID is required for close operation"
            );
        }

        AnomalyResponse response = AnomalyResponse.success(
                anomalySummary.getAnomalyId(),
                anomalySummary.getAccountIdentifier(),
                anomalySummary.getAnomalyStatus(),
                Constants.ANOMALY_OPERATION_CLOSE
        );

        try {
            // 1. Read existing anomaly from Redis and delete
            ViolationDetails violationDetails = redisUtilities.getViolationDetails(anomalySummary.getAccountIdentifier(), anomalySummary.getEntityIdentifier(), anomalySummary.getKpiId(), anomalySummary.getKpiAttribute(), anomalySummary.getViolationFor(), anomalySummary.getEntityType());
            if (violationDetails != null) {
                redisUtilities.deleteViolationDetails(anomalySummary.getAccountIdentifier(), anomalySummary.getEntityIdentifier(), anomalySummary.getKpiId(), anomalySummary.getKpiAttribute(), anomalySummary.getViolationFor(), anomalySummary.getEntityType());
            }

            // Try to read from OpenSearch heal_anomalies_* index as fallback
            var healAnomalyDoc = openSearchRepo.getHealAnomalyFromOpenSearch(anomalySummary.getAccountIdentifier(), anomalySummary.getAnomalyId());
            AnomalyRequest existingAnomaly = null;
                if (healAnomalyDoc != null) {
                    existingAnomaly = convertHealAnomalyToRequest(healAnomalyDoc, anomalySummary.getAccountIdentifier());
                }

            if (existingAnomaly == null) {
                return AnomalyResponse.failure(
                        anomalySummary.getAnomalyId(),
                        anomalySummary.getAccountIdentifier(),
                        "Close",
                        "Anomaly not found for close operation"
                );
            }

            // 2. Update anomaly status to CLOSED
            existingAnomaly.setAnomalyStatus("Close");
            existingAnomaly.setClosingReason(anomalySummary.getClosingReason());

            // 3. Update in OpenSearch (heal_anomalies_* index)
            openSearchRepo.updateHealAnomalyStatus(anomalySummary.getAccountIdentifier(), anomalySummary.getAnomalyId(), "Close",
                                                 System.currentTimeMillis(), anomalySummary.getClosingReason());
            log.debug("Anomaly status updated to CLOSE in heal_anomalies successfully");

            // 4. Publish close event to RabbitMQ
            publishToRabbitMQ(anomalyCloseQueueName, existingAnomaly, "Close");
            response.setRabbitMqProcessed(true);
            log.debug("Anomaly close event published to RabbitMQ successfully");

            // Update metrics
            metrics.updateSnapshots(anomalyCloseQueueName, 1);

            log.info("Anomaly closed successfully: {}", anomalySummary.getAnomalyId());
            return response;

        } catch (Exception e) {
            log.error("Error closing anomaly: {}", anomalySummary.getAnomalyId(), e);
            return AnomalyResponse.failure(
                    anomalySummary.getAnomalyId(),
                    anomalySummary.getAccountIdentifier(),
                    "Close",
                    "Failed to close anomaly: " + e.getMessage()
            );
        }
    }

    // Helper Methods

    /**
     * Publishes anomaly data to RabbitMQ queue
     */
    private void publishToRabbitMQ(String queueName, AnomalyRequest anomalyRequest, String operation) {
        try {
            Map<String, Object> messageData = new HashMap<>();
            messageData.put("operation", operation);
            messageData.put("anomalyData", anomalyRequest);
            messageData.put("timestamp", System.currentTimeMillis());

            String message = objectMapper.writeValueAsString(messageData);
            rabbitTemplate.convertAndSend(queueName, message);

            log.debug("Published {} operation to queue: {}", operation, queueName);
        } catch (Exception e) {
            log.error("Error publishing to RabbitMQ queue: {}, operation: {}", queueName, operation, e);
            throw new RuntimeException("Failed to publish to RabbitMQ", e);
        }
    }

    /**
     * Merges update data with existing anomaly data
     */
    private AnomalyRequest mergeAnomalyData(AnomalyRequest existing, AnomalyRequest updates) {
        AnomalyRequest merged = AnomalyRequest.builder()
                .anomalyId(existing.getAnomalyId())
                .accountIdentifier(existing.getAccountIdentifier())
                .entityId(updates.getEntityId() != null ? updates.getEntityId() : existing.getEntityId())
                .kpiId(updates.getKpiId() != 0 ? updates.getKpiId() : existing.getKpiId())
                .kpiAttribute(updates.getKpiAttribute() != null ? updates.getKpiAttribute() : existing.getKpiAttribute())
                .categoryId(updates.getCategoryId() != null ? updates.getCategoryId() : existing.getCategoryId())
                .serviceId(updates.getServiceId() != null ? updates.getServiceId() : existing.getServiceId())
                .thresholdType(updates.getThresholdType() != null ? updates.getThresholdType() : existing.getThresholdType())
                .operationType(updates.getOperationType() != null ? updates.getOperationType() : existing.getOperationType())
                .value(updates.getValue() != null ? updates.getValue() : existing.getValue())
                .lastThresholdsMeet(updates.getLastThresholdsMeet() != null ? updates.getLastThresholdsMeet() : existing.getLastThresholdsMeet())
                .metadata(mergeMetadata(existing.getMetadata(), updates.getMetadata()))
                .anomalyTime(existing.getAnomalyTime())
                .identifiedTime(existing.getIdentifiedTime())
                .kpiIdentifier(updates.getKpiIdentifier() != null ? updates.getKpiIdentifier() : existing.getKpiIdentifier())
                .closingReason(updates.getClosingReason() != null ? updates.getClosingReason() : existing.getClosingReason())
                .startSeverityId(updates.getStartSeverityId() != 0 ? updates.getStartSeverityId() : existing.getStartSeverityId())
                .lastSeverityId(updates.getLastSeverityId() != 0 ? updates.getLastSeverityId() : existing.getLastSeverityId())
                .build();

        return merged;
    }

    /**
     * Merges metadata maps
     */
    private Map<String, String> mergeMetadata(Map<String, String> existing, Map<String, String> updates) {
        Map<String, String> merged = new HashMap<>();
        if (existing != null) {
            merged.putAll(existing);
        }
        if (updates != null) {
            merged.putAll(updates);
        }
        return merged;
    }

    /**
     * Converts HealAnomalyDocument to AnomalyRequest
     */
    private AnomalyRequest convertHealAnomalyToRequest(HealAnomalyDocument healAnomaly, String accountIdentifier) {
        // Convert thresholds back to String format
        Map<String, Double> stringThresholds = new HashMap<>();
        if (healAnomaly.getLastThresholdsMeet() != null) {
            stringThresholds = new HashMap<>(healAnomaly.getLastThresholdsMeet());
        }

        // Convert metadata back to String format
        Map<String, String> stringMetadata = new HashMap<>();
        if (healAnomaly.getMetadata() != null) {
            healAnomaly.getMetadata().forEach((key, value) ->
                stringMetadata.put(key, String.valueOf(value)));
        }

        // Convert serviceId list to Set
        Set<String> serviceIds = null;
        if (healAnomaly.getServiceId() != null) {
            serviceIds = new HashSet<>(healAnomaly.getServiceId());
        }

        // Parse value
        Double doubleValue = null;
        if (healAnomaly.getValue() != null) {
            try {
                doubleValue = Double.parseDouble(healAnomaly.getValue());
            } catch (NumberFormatException e) {
                log.warn("Failed to parse anomaly value: {}", healAnomaly.getValue());
                doubleValue = 0.0;
            }
        }

        // Map status back to AnomalyStatus
        String status = "Open";
        if (healAnomaly.getAnomalyStatus() != null) {
            switch (healAnomaly.getAnomalyStatus()) {
                case "Open": status = "Open"; break;
                case "Ongoing": status = "Ongoing"; break;
                case "Close": status = "Close"; break;
                default: status = "Open"; break;
            }
        }

        return AnomalyRequest.builder()
                .anomalyId(healAnomaly.getAnomalyId())
                .accountIdentifier(accountIdentifier)
                .entityId(healAnomaly.getEntityId())
                .kpiId(healAnomaly.getKpiId())
                .kpiAttribute(healAnomaly.getKpiAttribute())
                .categoryId(healAnomaly.getCategoryId())
                .serviceId(serviceIds)
                .thresholdType(healAnomaly.getThresholdType())
                .operationType(healAnomaly.getOperationType())
                .value(String.valueOf(doubleValue))
                .lastThresholdsMeet(stringThresholds)
                .metadata(stringMetadata)
                .anomalyStatus(status)
                .anomalyTime(healAnomaly.getAnomalyTime())
                .identifiedTime(healAnomaly.getIdentifiedTime())
                .kpiIdentifier(healAnomaly.getKpiIdentifier())
                .lastSeverityId(healAnomaly.getLastSeverityId())
                .build();
    }
}