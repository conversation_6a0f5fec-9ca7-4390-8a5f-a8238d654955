package com.heal.event.detector.scheduler;

import com.heal.event.detector.config.OpenSearchConfig;
import com.heal.event.detector.pojos.ClientBulkRequestPojo;
import com.heal.event.detector.pojos.IndexRequestInsertDetails;
import com.heal.event.detector.pojos.UpdateByQueryRequestInsertDetails;
import com.heal.event.detector.pojos.UpdateRequestInsertDetails;
import com.heal.event.detector.utility.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch.core.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 14-02-2022
 */
@Slf4j
@Service
public class OSDataPushScheduler {
    @Value("${opensearch.batch.size:500}")
    private int osBatchSize;
    @Value("${opensearch.batch.queue.max.size:50000}")
    private int osMaxQueueSize;
    @Autowired
    OpenSearchConfig openSearchConfig;
    @Autowired
    HealthMetrics metrics;

    private Queue<IndexRequestInsertDetails> indexRequestQueue = null;
    private Queue<UpdateRequestInsertDetails> updateRequestQueue = null;
    private Queue<UpdateByQueryRequestInsertDetails> updateByQueryRequestQueue = null;

    @PostConstruct
    public void postConstruct() {
        indexRequestQueue = new ArrayBlockingQueue<>(osMaxQueueSize);
        updateRequestQueue = new ArrayBlockingQueue<>(osMaxQueueSize);
        updateByQueryRequestQueue = new ArrayBlockingQueue<>(osMaxQueueSize);
    }

    public void addToIndexQueue(IndexRequest<?> indexRequest, String accIdentifier, String indexName) {
        try {
            indexRequest = indexRequest.toBuilder().index(indexRequest.index().toLowerCase()).build();
            indexRequestQueue.offer(IndexRequestInsertDetails.builder().indexRequest(indexRequest).accountIdentifier(accIdentifier).indexName(indexName).build());
        } catch (Exception e) {
            log.error("Maximum queue limit {} breached for index request.", osMaxQueueSize);
        }
    }

    public void addToUpdateQueue(UpdateRequest<?, ?> updateRequest, String accIdentifier, String indexName) {
        try {
            updateRequest = updateRequest.toBuilder().index(updateRequest.index().toLowerCase()).build();
            updateRequestQueue.offer(UpdateRequestInsertDetails.builder().updateRequest(updateRequest).accountIdentifier(accIdentifier).indexName(indexName).build());
        } catch (Exception e) {
            log.error("Maximum queue queue limit {} breached for update request.", osMaxQueueSize);
        }
    }

    public void addToUpdateByQueryQueue(UpdateByQueryRequest updateByQueryRequest, String accIdentifier, String indexName) {
        try {
            updateByQueryRequest = updateByQueryRequest.toBuilder().index(updateByQueryRequest.index().stream().map(String::toLowerCase).collect(Collectors.toList())).build();
            updateByQueryRequestQueue.offer(UpdateByQueryRequestInsertDetails.builder().updateByQueryRequest(updateByQueryRequest).accountIdentifier(accIdentifier).indexName(indexName).build());
        } catch (Exception e) {
            log.error("Maximum queue queue limit {} breached for update by query request.", osMaxQueueSize);
        }
    }

    public long getIndexRequestQueueSize() {
        return indexRequestQueue.size();
    }

    public long getUpdateRequestQueueSize() {
        return updateRequestQueue.size();
    }

    public long getOpenSearchMaxQueueSize() {
        return osMaxQueueSize;
    }

    @Scheduled(initialDelayString = "${opensearch.data.push.schedule.initial.delay:2}", fixedRateString = "${opensearch.data.push.schedule.interval:5}", timeUnit = TimeUnit.SECONDS)
    public void pushToOSBulkIndexing() {
        long st = System.currentTimeMillis();
        List<IndexRequestInsertDetails> indexRequestList = new ArrayList<>();
        List<UpdateRequestInsertDetails> updateRequestList = new ArrayList<>();
        List<UpdateByQueryRequestInsertDetails> updateByQueryRequestList = new ArrayList<>();

        try {
            int dataCounter = -1;
            while (indexRequestQueue.peek() != null && ++dataCounter < osBatchSize) {
                IndexRequestInsertDetails index = indexRequestQueue.poll();
                indexRequestList.add(index);
            }
            dataCounter = -1;
            while (updateRequestQueue.peek() != null && ++dataCounter < osBatchSize) {
                UpdateRequestInsertDetails updateRequest = updateRequestQueue.poll();
                updateRequestList.add(updateRequest);
            }
            dataCounter = -1;
            while (updateByQueryRequestQueue.peek() != null && ++dataCounter < osBatchSize) {
                UpdateByQueryRequestInsertDetails updateByQueryRequest = updateByQueryRequestQueue.poll();
                updateByQueryRequestList.add(updateByQueryRequest);
            }

            Map<String, ClientBulkRequestPojo> accZoneRestClientMap = new HashMap<>();

            indexRequestList.forEach(c -> {
                String accZoneKey = c.getAccountIdentifier() + "_" + c.getIndexName();
                if (!accZoneRestClientMap.containsKey(accZoneKey)) {
                    OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(c.getAccountIdentifier(), c.getIndexName());
                    if (openSearchClient == null) {
                        log.error("NULL OpenSearch client for account {}, index {}. Skipping the index request.", c.getAccountIdentifier(), c.getIndexName());
                        return;
                    }

                    BulkRequest bulkRequest = new BulkRequest.Builder()
                            .operations(op ->
                                    op.index(idx -> idx
                                            .index(c.getIndexRequest().index())
                                            .id(c.getIndexRequest().id())
                                            .document(c.getIndexRequest().document())
                                    ))
                            .build();

                    accZoneRestClientMap.put(c.getAccountIdentifier() + "_" + c.getIndexName(), ClientBulkRequestPojo.builder()
                            .bulkRequest(bulkRequest)
                            .openSearchClient(openSearchClient)
                            .build());
                } else {
                    BulkRequest bulkRequest = accZoneRestClientMap.get(accZoneKey).getBulkRequest().toBuilder().operations(op ->
                                    op.index(idx -> idx
                                            .index(c.getIndexRequest().index())
                                            .id(c.getIndexRequest().id())
                                            .document(c.getIndexRequest().document())
                                    ))
                            .build();

                    accZoneRestClientMap.get(accZoneKey).setBulkRequest(bulkRequest);
                }
                metrics.updateOpenSearchBatchInserts();
            });

            updateRequestList.forEach(c -> {
                String accZoneKey = c.getAccountIdentifier() + "_" + c.getIndexName();
                if (!accZoneRestClientMap.containsKey(accZoneKey)) {
                    OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(c.getAccountIdentifier(), c.getIndexName());
                    if (openSearchClient == null) {
                        log.error("NULL OpenSearch client for account {}, zone {}. Skipping the update request.", c.getAccountIdentifier(), c.getIndexName());
                        return;
                    }

                    BulkRequest bulkRequest = new BulkRequest.Builder()
                            .operations(op ->
                                    op.update(idx -> idx
                                            .index(c.getUpdateRequest().index())
                                            .id(c.getUpdateRequest().id())
                                            .document(c.getUpdateRequest().doc())
                                    ))
                            .build();

                    accZoneRestClientMap.put(c.getAccountIdentifier() + "_" + c.getIndexName(), ClientBulkRequestPojo.builder()
                            .bulkRequest(bulkRequest)
                            .openSearchClient(openSearchClient)
                            .build());
                } else {
                    BulkRequest bulkRequest = accZoneRestClientMap.get(accZoneKey).getBulkRequest().toBuilder().operations(op ->
                                    op.update(idx -> idx
                                            .index(c.getUpdateRequest().index())
                                            .id(c.getUpdateRequest().id())
                                            .document(c.getUpdateRequest().doc())
                                    ))
                            .build();

                    accZoneRestClientMap.get(accZoneKey).setBulkRequest(bulkRequest);
                }
                metrics.updateOpenSearchUpdates();
            });

            try {
                accZoneRestClientMap.values().forEach(c ->
                        CompletableFuture.runAsync(() -> {
                            try {
                                BulkResponse response = c.getOpenSearchClient().bulk(c.getBulkRequest());
                                if (response.errors()) {
                                    response.items().forEach(item -> {
                                        if (item.error() != null) {
                                            log.error("Failures during bulk indexing operation. Exception type: [{}], Reason: [{}]", item.error().type(),
                                                    item.error().causedBy() != null ? item.error().causedBy().reason() : item.error().reason());
                                            metrics.updateOpenSearchWriteErrors();
                                        }
                                    });
                                } else {
                                    log.debug("Bulk indexing operation successful!");
                                }
                            } catch (Exception e) {
                                log.error("Exception during bulk indexing operation: ", e);
                                metrics.updateOpenSearchWriteErrors();
                            }
                        }));
            } catch (Exception e) {
                log.error("Exception while bulk operation in OpenSearch.", e);
                metrics.updateOpenSearchWriteErrors();
            }

            try {
                updateByQueryRequestList.forEach(c -> {
                    OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(c.getAccountIdentifier(), c.getIndexName());
                    if (openSearchClient == null) {
                        log.error("NULL OpenSearch client for account {}, zone {}. Skipping the update by query request.", c.getAccountIdentifier(), c.getIndexName());
                        return;
                    }

                    CompletableFuture.runAsync(() -> {
                        try {
                            UpdateByQueryResponse updateByQueryResponse = openSearchClient.updateByQuery(c.getUpdateByQueryRequest());
                            if (!updateByQueryResponse.failures().isEmpty()) {
                                updateByQueryResponse.failures().forEach(item -> {
                                    log.error("Failures during update by query request in index: {}, id: {}, Cause:{}", item.index(), item.id(), item.cause());
                                    metrics.updateOpenSearchWriteErrors();
                                });
                            }
                        } catch (Exception e) {
                            log.error("Exception during updateByQuery request: ", e);
                            metrics.updateOpenSearchWriteErrors();
                        }
                    });
                });
            } catch (Exception e) {
                log.error("Exception while updatingByQuery data in OpenSearch.", e);
                metrics.updateOpenSearchWriteErrors();
            }
        } catch (Exception e) {
            log.error("Exception while scheduling data to push into OpenSearch. ", e);
        } finally {
            long t = System.currentTimeMillis() - st;
            log.debug("Pushed data of size {} to OpenSearch, time taken {} ms.", indexRequestList.size(), t);
            log.debug("Updated data of size {} in OpenSearch, time taken {} ms.", updateRequestList.size(), t);
            log.debug("Updated by queryRequest data of size {} in OpenSearch, time taken {} ms.", updateByQueryRequestList.size(), t);
        }
    }

}
