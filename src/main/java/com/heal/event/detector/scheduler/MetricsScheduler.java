package com.heal.event.detector.scheduler;

import com.heal.event.detector.utility.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MetricsScheduler {

    @Autowired
    private HealthMetrics healthMetrics;

    @Scheduled(initialDelay = 1000, fixedRateString = "${health.metrics.update.interval.milliseconds:10000}")
    public void updateSnapshots() {
        healthMetrics.resetSnapshots();
        log.info("Metrics scheduler method called.");
    }
}
