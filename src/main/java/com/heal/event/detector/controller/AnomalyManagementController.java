package com.heal.event.detector.controller;

import com.heal.event.detector.pojos.AnomalyRequest;
import com.heal.event.detector.pojos.AnomalyResponse;
import com.heal.event.detector.service.AnomalyManagementService;
import com.heal.event.detector.utility.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * REST Controller for Heal Anomaly Management operations
 * Provides HTTP endpoints for creating, updating, and closing anomalies
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/anomalies")
public class AnomalyManagementController {

    @Autowired
    private AnomalyManagementService anomalyManagementService;

    @Autowired
    private HealthMetrics metrics;

    @Value("${heal.anomaly.api.auth.enabled:false}")
    private boolean apiAuthEnabled;

    @Value("${heal.anomaly.api.rate.limit.enabled:true}")
    private boolean rateLimitEnabled;

    /**
     * Create a new anomaly
     * 
     * @param anomalyRequest The anomaly data to create
     * @return ResponseEntity with AnomalyResponse
     */
    @PostMapping
    public ResponseEntity<AnomalyResponse> createAnomaly(@Valid @RequestBody AnomalyRequest anomalyRequest) {
        log.info("REST API: Creating anomaly for account: {}", anomalyRequest.getAccountIdentifier());
        
        try {
            long startTime = System.currentTimeMillis();
            AnomalyResponse response = anomalyManagementService.createAnomaly(anomalyRequest);
            long processingTime = System.currentTimeMillis() - startTime;
            
            metrics.updateHealAnomalyProcessingTime(processingTime);
            
            if (response.isSuccess()) {
                metrics.updateHealAnomaliesCreated();
                log.info("REST API: Anomaly created successfully: {}", response.getAnomalyId());
                return ResponseEntity.status(HttpStatus.CREATED).body(response);
            } else {
                metrics.updateHealAnomalyProcessingErrors();
                log.error("REST API: Failed to create anomaly: {}", response.getMessage());
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }
        } catch (Exception e) {
            metrics.updateHealAnomalyProcessingErrors();
            log.error("REST API: Exception while creating anomaly", e);
            
            AnomalyResponse errorResponse = AnomalyResponse.builder()
                    .success(false)
                    .message("Internal server error: " + e.getMessage())
                    .timestamp(System.currentTimeMillis())
                    .build();
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Update an existing anomaly
     * 
     * @param anomalyId The ID of the anomaly to update
     * @param anomalyRequest The updated anomaly data
     * @return ResponseEntity with AnomalyResponse
     */
    @PutMapping("/{anomalyId}")
    public ResponseEntity<AnomalyResponse> updateAnomaly(
            @PathVariable String anomalyId,
            @Valid @RequestBody AnomalyRequest anomalyRequest) {
        
        log.info("REST API: Updating anomaly: {}", anomalyId);
        
        try {
            // Ensure the anomaly ID in the path matches the request body
            anomalyRequest.setAnomalyId(anomalyId);
            
            long startTime = System.currentTimeMillis();
            AnomalyResponse response = anomalyManagementService.updateAnomaly(anomalyRequest);
            long processingTime = System.currentTimeMillis() - startTime;
            
            metrics.updateHealAnomalyProcessingTime(processingTime);
            
            if (response.isSuccess()) {
                metrics.updateHealAnomaliesUpdated();
                log.info("REST API: Anomaly updated successfully: {}", response.getAnomalyId());
                return ResponseEntity.ok(response);
            } else {
                metrics.updateHealAnomalyProcessingErrors();
                log.error("REST API: Failed to update anomaly: {}", response.getMessage());
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }
        } catch (Exception e) {
            metrics.updateHealAnomalyProcessingErrors();
            log.error("REST API: Exception while updating anomaly: {}", anomalyId, e);
            
            AnomalyResponse errorResponse = AnomalyResponse.builder()
                    .anomalyId(anomalyId)
                    .success(false)
                    .message("Internal server error: " + e.getMessage())
                    .timestamp(System.currentTimeMillis())
                    .build();
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Close an existing anomaly
     * 
     * @param anomalyId The ID of the anomaly to close
     * @param accountIdentifier The account identifier
     * @param reason The reason for closing (optional)
     * @param closedBy Who closed the anomaly (optional)
     * @return ResponseEntity with AnomalyResponse
     */
    @DeleteMapping("/{anomalyId}")
    public ResponseEntity<AnomalyResponse> closeAnomaly(
            @PathVariable String anomalyId,
            @RequestParam String accountIdentifier,
            @RequestParam(required = false, defaultValue = "Closed via REST API") String reason,
            @RequestParam(required = false, defaultValue = "api-user") String closedBy) {
        
        log.info("REST API: Closing anomaly: {} for account: {}", anomalyId, accountIdentifier);
        
        try {
            long startTime = System.currentTimeMillis();
            AnomalyResponse response = anomalyManagementService.closeAnomaly(anomalyId, accountIdentifier, reason, closedBy);
            long processingTime = System.currentTimeMillis() - startTime;
            
            metrics.updateHealAnomalyProcessingTime(processingTime);
            
            if (response.isSuccess()) {
                metrics.updateHealAnomaliesClosed();
                log.info("REST API: Anomaly closed successfully: {}", response.getAnomalyId());
                return ResponseEntity.ok(response);
            } else {
                metrics.updateHealAnomalyProcessingErrors();
                log.error("REST API: Failed to close anomaly: {}", response.getMessage());
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }
        } catch (Exception e) {
            metrics.updateHealAnomalyProcessingErrors();
            log.error("REST API: Exception while closing anomaly: {}", anomalyId, e);
            
            AnomalyResponse errorResponse = AnomalyResponse.builder()
                    .anomalyId(anomalyId)
                    .accountIdentifier(accountIdentifier)
                    .success(false)
                    .message("Internal server error: " + e.getMessage())
                    .timestamp(System.currentTimeMillis())
                    .build();
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Get health status of anomaly management system
     * 
     * @return ResponseEntity with health metrics
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> getHealth() {
        try {
            Map<String, Object> health = new HashMap<>();
            health.put("status", "UP");
            health.put("timestamp", System.currentTimeMillis());
            health.put("metrics", Map.of(
                    "anomaliesCreated", metrics.getHealAnomaliesCreated(),
                    "anomaliesUpdated", metrics.getHealAnomaliesUpdated(),
                    "anomaliesClosed", metrics.getHealAnomaliesClosed(),
                    "alertsCreated", metrics.getHealAlertsCreated(),
                    "processingErrors", metrics.getHealAnomalyProcessingErrors(),
                    "averageProcessingTime", metrics.getHealAnomalyAverageProcessingTime(),
                    "totalProcessingCount", metrics.getHealAnomalyProcessingCount()
            ));
            
            return ResponseEntity.ok(health);
        } catch (Exception e) {
            log.error("REST API: Exception while getting health status", e);
            
            Map<String, Object> errorHealth = new HashMap<>();
            errorHealth.put("status", "DOWN");
            errorHealth.put("timestamp", System.currentTimeMillis());
            errorHealth.put("error", e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorHealth);
        }
    }

    /**
     * Get metrics for anomaly management system
     * 
     * @return ResponseEntity with detailed metrics
     */
    @GetMapping("/metrics")
    public ResponseEntity<Map<String, Object>> getMetrics() {
        try {
            Map<String, Object> metricsData = new HashMap<>();
            metricsData.put("timestamp", System.currentTimeMillis());
            metricsData.put("healAnomalies", Map.of(
                    "created", metrics.getHealAnomaliesCreated(),
                    "updated", metrics.getHealAnomaliesUpdated(),
                    "closed", metrics.getHealAnomaliesClosed()
            ));
            metricsData.put("healAlerts", Map.of(
                    "created", metrics.getHealAlertsCreated()
            ));
            metricsData.put("processing", Map.of(
                    "errors", metrics.getHealAnomalyProcessingErrors(),
                    "averageTime", metrics.getHealAnomalyAverageProcessingTime(),
                    "totalCount", metrics.getHealAnomalyProcessingCount()
            ));
            metricsData.put("system", Map.of(
                    "openSearchErrors", metrics.getOpenSearchErrors(),
                    "rmqReadErrors", metrics.getRmqReadErrors(),
                    "rmqWriteErrors", metrics.getRmqWriteErrors()
            ));
            
            return ResponseEntity.ok(metricsData);
        } catch (Exception e) {
            log.error("REST API: Exception while getting metrics", e);
            
            Map<String, Object> errorMetrics = new HashMap<>();
            errorMetrics.put("error", e.getMessage());
            errorMetrics.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorMetrics);
        }
    }
}
