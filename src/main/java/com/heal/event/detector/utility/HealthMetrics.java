package com.heal.event.detector.utility;

import com.heal.event.detector.scheduler.OSDataPushScheduler;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.jmx.export.annotation.ManagedAttribute;
import org.springframework.jmx.export.annotation.ManagedResource;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@ManagedResource(objectName = "EventDetector:name=ApplicationInfo")
public class HealthMetrics {

    @Autowired
    private ThreadPoolTaskExecutor threadPoolExecutor;

    @Lazy
    @Autowired
    private OSDataPushScheduler scheduler;

    @Lazy
    @Autowired
    LocalCache localCache;

    @Value("${spring.rabbitmq.thresholdForAggregatedKPIInSecs:3}")
    private int thresholdForAggregatedKPI;

    @Value("${spring.rabbitmq.thresholdForViolatedEventInSecs:3}")
    private int thresholdForViolatedEvent;

    @Value("${spring.rabbitmq.thresholdForNorThresholdInSecs:3}")
    private int thresholdForNorThreshold;
    @Value("${spring.rabbitmq.thresholdForNorClosedThresholdInSecs:3}")
    private int thresholdForNorClosedThreshold;

    private long readCountAggregatedKpiQueue = 0;
    private long kpisProcessedForAggregateKPIs = 0;
    private long readCountViolatedEventQueue = 0;
    private long kpisProcessedForViolatedEvents = 0;
    private long readCountNorThresholdQueue = 0;
    private long readCountNorClosedThresholdQueue = 0;
    private long kpisProcessedForNorThreshold = 0;
    private long kpisProcessedForNorClosedThreshold = 0;
    private long writeCountAnomalySignalQueue = 0;
    private long writeCountAnomalyActionQueue = 0;
    private long writeCountAnomalyMessagesQueue = 0;

    @Setter
    private String readAggregatedKpiQueueName;
    @Setter
    private String readViolatedEventQueueName;
    @Setter
    private String readNorThresholdQueueName;
    @Setter
    private String readNorClosedThresholdQueueName;
    @Setter
    private String writeAnomalyOutputSignalQueueName;
    @Setter
    private String writeAnomalyOutputActionQueueName;
    @Setter
    private String writeAnomalyOutputMLESignalQueueName;
    @Setter
    private String writeAnomalyMessagesQueueName;

    private long violationCount = 0;
    private long anomalyCount = 0;
    private long redisKeysNotFound = 0;

    private long aggregatedKpiProcessingErrors = 0;
    private long violatedEventProcessingErrors = 0;
    private long norThresholdProcessingErrors = 0;
    private long norClosedThresholdProcessingErrors = 0;

    private long rmqReadErrors = 0;
    private long rmqWriteErrors = 0;

    private long minProcessTimeAggregatedKPI = 0;
    private long maxProcessTimeAggregatedKPI = 0;
    private long slowAggregatedKpiEvents = 0;

    private long minProcessTimeViolatedEvent = 0;
    private long maxProcessTimeViolatedEvent = 0;
    private long slowViolatedEvents = 0;

    private long minTimeNorThreshold = 0;
    private long maxTimeNorThreshold = 0;
    private long slowNorThresholdEvents = 0;

    private long minTimeNorClosedThreshold = 0;
    private long maxTimeNorClosedThreshold = 0;
    private long slowNorClosedThresholdEvents = 0;

    private long openSearchErrors = 0;
    private long openSearchWriteErrors = 0;
    private long openSearchBatchInserts = 0;
    private long openSearchUpdates = 0;
    private long openSearchGetRequests = 0;

    private long instanceKpiThresholdsProcessed = 0;
    private long transactionKpiThresholdsProcessed = 0;

    private long duplicateNorThresholds = 0;
    private long oldNorThresholds = 0;

    private long instanceKpiThresholdsReceived = 0;
    private long transactionKpiThresholdsReceived = 0;
    private long delayQueueRemovedKey = 0;

    private Map<String, Integer> snapshots = new HashMap<>();

    @ManagedAttribute
    public int getWorkerPoolSize() {
        return threadPoolExecutor.getPoolSize();
    }

    @ManagedAttribute
    public int getWorkerActiveSize() {
        return threadPoolExecutor.getActiveCount();
    }

    @ManagedAttribute
    public int getWorkerQueueSize() {
        return threadPoolExecutor.getThreadPoolExecutor().getQueue().size();
    }

    @ManagedAttribute
    public long getReadCountAggregatedKpiQueue() {
        return readCountAggregatedKpiQueue;
    }

    public void updateReadCountAggregatedKpiQueue() {
        if (readCountAggregatedKpiQueue == Long.MAX_VALUE) {
            readCountAggregatedKpiQueue = 0;
        }
        readCountAggregatedKpiQueue++;
    }

    @ManagedAttribute
    public long getReadCountViolatedEventQueue() {
        return readCountViolatedEventQueue;
    }

    public void updateReadCountViolatedEventQueue() {
        if (readCountViolatedEventQueue == Long.MAX_VALUE) {
            readCountViolatedEventQueue = 0;
        }
        readCountViolatedEventQueue++;
    }

    @ManagedAttribute
    public long getReadCountNorThresholdQueue() {
        return readCountNorThresholdQueue;
    }

    public void updateReadCountNorThresholdQueue() {
        if (readCountNorThresholdQueue == Long.MAX_VALUE) {
            readCountNorThresholdQueue = 0;
        }
        readCountNorThresholdQueue++;
    }

    @ManagedAttribute
    public long getReadCountNorClosedThresholdQueue() {
        return readCountNorClosedThresholdQueue;
    }

    public void updateReadCountNorClosedThresholdQueue() {
        if (readCountNorClosedThresholdQueue == Long.MAX_VALUE) {
            readCountNorClosedThresholdQueue = 0;
        }
        readCountNorClosedThresholdQueue++;
    }

    @ManagedAttribute
    public long getWriteCountAnomalySignalQueue() {
        return writeCountAnomalySignalQueue;
    }

    public void updateWriteCountAnomalySignalQueue(int count) {
        if (writeCountAnomalySignalQueue == Long.MAX_VALUE) {
            writeCountAnomalySignalQueue = 0;
        }
        writeCountAnomalySignalQueue += count;
    }

    @ManagedAttribute
    public long getWriteCountAnomalyActionQueue() {
        return writeCountAnomalyActionQueue;
    }

    public void updateWriteCountAnomalyActionQueue(int count) {
        if (writeCountAnomalyActionQueue == Long.MAX_VALUE) {
            writeCountAnomalyActionQueue = 0;
        }
        writeCountAnomalyActionQueue += count;
    }

    @ManagedAttribute
    public long getWriteCountAnomalyMessagesQueue() {
        return writeCountAnomalyMessagesQueue;
    }

    public void updateWriteCountAnomalyMessagesQueue(int count) {
        if (writeCountAnomalyMessagesQueue == Long.MAX_VALUE) {
            writeCountAnomalyMessagesQueue = 0;
        }
        writeCountAnomalyMessagesQueue += count;
    }


    public void resetSnapshots() {
        snapshots = new HashMap<>();
    }

    @ManagedAttribute
    public Map<String, Integer> getSnapshots() {
        return snapshots;
    }

    public void updateSnapshots(String keyName, int value) {
        snapshots.put(keyName, snapshots.getOrDefault(keyName, 0) + value);
    }

    @ManagedAttribute
    public String getReadAggregatedKpiQueueName() {
        return readAggregatedKpiQueueName;
    }

    @ManagedAttribute
    public String getReadViolatedEventQueueName() {
        return readViolatedEventQueueName;
    }

    @ManagedAttribute
    public String getReadNorThresholdQueueName() {
        return readNorThresholdQueueName;
    }

    @ManagedAttribute
    public String getReadNorClosedThresholdQueueName() {
        return readNorClosedThresholdQueueName;
    }

    @ManagedAttribute
    public String getWriteAnomalyOutputSignalQueueName() {
        return writeAnomalyOutputSignalQueueName;
    }

    @ManagedAttribute
    public String getWriteAnomalyOutputMLESignalQueueName() {
        return writeAnomalyOutputMLESignalQueueName;
    }

    @ManagedAttribute
    public String getWriteAnomalyMessagesQueueName() {
        return writeAnomalyMessagesQueueName;
    }

    @ManagedAttribute
    public String getWriteAnomalyOutputActionQueueName() {
        return writeAnomalyOutputActionQueueName;
    }

    @ManagedAttribute
    public long getViolationCount() {
        return violationCount;
    }

    public void updateViolationCount(int count) {
        if (violationCount == Long.MAX_VALUE) {
            violationCount = 0;
        }
        violationCount += count;
    }

    @ManagedAttribute
    public long getAnomalyCount() {
        return anomalyCount;
    }

    public void updateAnomalyCount(int count) {
        if (anomalyCount == Long.MAX_VALUE) {
            anomalyCount = 0;
        }
        anomalyCount += count;
    }

    @ManagedAttribute
    public long getRedisKeysNotFound() {
        return redisKeysNotFound;
    }

    public void updateRedisKeysNotFound() {
        if (redisKeysNotFound == Long.MAX_VALUE) {
            redisKeysNotFound = 0;
        }
        redisKeysNotFound++;
    }

    @ManagedAttribute
    public long getAggregatedKpiProcessingErrors() {
        return aggregatedKpiProcessingErrors;
    }

    public void updateAggregatedKpiProcessingErrors() {
        if (aggregatedKpiProcessingErrors == Long.MAX_VALUE) {
            aggregatedKpiProcessingErrors = 0;
        }
        aggregatedKpiProcessingErrors++;
    }

    @ManagedAttribute
    public long getViolatedEventProcessingErrors() {
        return violatedEventProcessingErrors;
    }

    public void updateViolatedEventProcessingErrors() {
        if (violatedEventProcessingErrors == Long.MAX_VALUE) {
            violatedEventProcessingErrors = 0;
        }
        violatedEventProcessingErrors++;
    }

    @ManagedAttribute
    public long getNorThresholdProcessingErrors() {
        return norThresholdProcessingErrors;
    }

    public void updateNorThresholdProcessingErrors() {
        if (norThresholdProcessingErrors == Long.MAX_VALUE) {
            norThresholdProcessingErrors = 0;
        }
        norThresholdProcessingErrors++;
    }

    @ManagedAttribute
    public long getNorClosedThresholdProcessingErrors() {
        return norClosedThresholdProcessingErrors;
    }

    public void updateNorClosedThresholdProcessingErrors() {
        if (norClosedThresholdProcessingErrors == Long.MAX_VALUE) {
            norClosedThresholdProcessingErrors = 0;
        }
        norClosedThresholdProcessingErrors++;
    }

    @ManagedAttribute
    public long getRmqReadErrors() {
        if (rmqReadErrors == Long.MAX_VALUE) {
            rmqReadErrors = 0;
        }
        return rmqReadErrors;
    }

    public void updateRmqReadErrors() {
        rmqReadErrors++;
    }

    @ManagedAttribute
    public long getRmqWriteErrors() {
        if (rmqWriteErrors == Long.MAX_VALUE) {
            rmqWriteErrors = 0;
        }
        return rmqWriteErrors;
    }

    public void updateRmqWriteErrors(long count) {
        rmqWriteErrors += count;
    }

    @ManagedAttribute
    public long getKpisProcessedForAggregateKPIs() {
        return kpisProcessedForAggregateKPIs;
    }

    public void updateMessagesProcessedForAggregatedKPIs(int count) {
        if (kpisProcessedForAggregateKPIs == Long.MAX_VALUE) {
            kpisProcessedForAggregateKPIs = 0;
        }
        kpisProcessedForAggregateKPIs += count;
    }

    @ManagedAttribute
    public long getMinProcessTimeAggregatedKPI() {
        return minProcessTimeAggregatedKPI;
    }

    public void updateMinProcessTimeAggregatedKPI(long time) {
        if (kpisProcessedForAggregateKPIs <= 1) {
            minProcessTimeAggregatedKPI = time;
        }
        if (time < minProcessTimeAggregatedKPI) {
            minProcessTimeAggregatedKPI = time;
        }
    }

    @ManagedAttribute
    public long getMaxProcessTimeAggregatedKPI() {
        return maxProcessTimeAggregatedKPI;
    }

    public void updateMaxProcessTimeAggregatedKPI(long time) {
        if (time > maxProcessTimeAggregatedKPI) {
            maxProcessTimeAggregatedKPI = time;
        }
    }

    @ManagedAttribute
    public long getThresholdForAggregatedKPI() {
        return thresholdForAggregatedKPI;
    }

    @ManagedAttribute
    public long getSlowAggregatedKpiEvents() {
        return slowAggregatedKpiEvents;
    }

    public void updateSlowAggregatedKpiEvents(long l) {
        updateMessagesProcessedForAggregatedKPIs(1);
        updateMinProcessTimeAggregatedKPI(l);
        updateMaxProcessTimeAggregatedKPI(l);
        if (slowAggregatedKpiEvents == Long.MAX_VALUE) {
            slowAggregatedKpiEvents = 0;
        }
        if (thresholdForAggregatedKPI * 1000L < l)
            slowAggregatedKpiEvents++;
    }

    @ManagedAttribute
    public long getKpisProcessedForViolatedEvents() {
        return kpisProcessedForViolatedEvents;
    }

    public void updateMessagesProcessedForViolatedEvents(int count) {
        if (kpisProcessedForViolatedEvents == Long.MAX_VALUE) {
            kpisProcessedForViolatedEvents = 0;
        }
        kpisProcessedForViolatedEvents += count;
    }

    @ManagedAttribute
    public long getMinProcessTimeViolatedEvent() {
        return minProcessTimeViolatedEvent;
    }

    public void updateMinProcessTimeViolatedEvent(long time) {
        if (kpisProcessedForViolatedEvents <= 1) {
            minProcessTimeViolatedEvent = time;
        }
        if (time < minProcessTimeViolatedEvent) {
            minProcessTimeViolatedEvent = time;
        }
    }

    @ManagedAttribute
    public long getMaxProcessTimeViolatedEvent() {
        return maxProcessTimeViolatedEvent;
    }

    public void updateMaxProcessTimeViolatedEvent(long time) {
        if (time > maxProcessTimeViolatedEvent) {
            maxProcessTimeViolatedEvent = time;
        }
    }

    @ManagedAttribute
    public long getThresholdForViolatedEvent() {
        return thresholdForViolatedEvent;
    }

    @ManagedAttribute
    public long getSlowViolatedEvents() {
        return slowViolatedEvents;
    }

    public void updateSlowViolatedEvents(long l) {
        updateMessagesProcessedForViolatedEvents(1);
        updateMinProcessTimeViolatedEvent(l);
        updateMaxProcessTimeViolatedEvent(l);
        if (slowViolatedEvents == Long.MAX_VALUE) {
            slowViolatedEvents = 0;
        }
        if (thresholdForViolatedEvent * 1000L < l)
            slowViolatedEvents++;
    }

    @ManagedAttribute
    public long getKpisProcessedForNorThreshold() {
        return kpisProcessedForNorThreshold;
    }

    public void updateMessagesProcessedForNorThreshold(int count) {
        if (kpisProcessedForNorThreshold == Long.MAX_VALUE) {
            kpisProcessedForNorThreshold = 0;
        }
        kpisProcessedForNorThreshold += count;
    }

    @ManagedAttribute
    public long getMinTimeNorThreshold() {
        return minTimeNorThreshold;
    }

    public void updateMinTimeNorThreshold(long time) {
        if (kpisProcessedForNorThreshold <= 1) {
            minTimeNorThreshold = time;
        }
        if (time < minTimeNorThreshold) {
            minTimeNorThreshold = time;
        }
    }

    @ManagedAttribute
    public long getMaxTimeNorThreshold() {
        return maxTimeNorThreshold;
    }

    public void updateMaxTimeNorThreshold(long time) {
        if (time > maxTimeNorThreshold) {
            maxTimeNorThreshold = time;
        }
    }

    @ManagedAttribute
    public long getThresholdForNorThreshold() {
        return thresholdForNorThreshold;
    }

    @ManagedAttribute
    public long getSlowNorThresholdEvents() {
        return slowNorThresholdEvents;
    }

    public void updateSlowNorThresholdEvents(long l) {
        updateMessagesProcessedForNorThreshold(1);
        updateMinTimeNorThreshold(l);
        updateMaxTimeNorThreshold(l);
        if (slowNorThresholdEvents == Long.MAX_VALUE) {
            slowNorThresholdEvents = 0;
        }
        if (thresholdForNorThreshold * 1000L < l)
            slowNorThresholdEvents++;
    }

    @ManagedAttribute
    public long getKpisProcessedForNorClosedThreshold() {
        return kpisProcessedForNorClosedThreshold;
    }

    public void updateMessagesProcessedForNorClosedThreshold(int count) {
        if (kpisProcessedForNorClosedThreshold == Long.MAX_VALUE) {
            kpisProcessedForNorClosedThreshold = 0;
        }
        kpisProcessedForNorClosedThreshold += count;
    }

    @ManagedAttribute
    public long getMinTimeNorClosedThreshold() {
        return minTimeNorClosedThreshold;
    }

    public void updateMinTimeNorClosedThreshold(long time) {
        if (kpisProcessedForNorClosedThreshold <= 1) {
            minTimeNorClosedThreshold = time;
        }
        if (time < minTimeNorClosedThreshold) {
            minTimeNorClosedThreshold = time;
        }
    }

    @ManagedAttribute
    public long getMaxTimeNorClosedThreshold() {
        return maxTimeNorClosedThreshold;
    }

    public void updateMaxTimeNorClosedThreshold(long time) {
        if (time > maxTimeNorClosedThreshold) {
            maxTimeNorClosedThreshold = time;
        }
    }

    @ManagedAttribute
    public long getThresholdForNorClosedThreshold() {
        return thresholdForNorClosedThreshold;
    }

    public void updateSlowNorClosedThresholdEvents(long l) {
        updateMessagesProcessedForNorClosedThreshold(1);
        updateMinTimeNorClosedThreshold(l);
        updateMaxTimeNorClosedThreshold(l);
        if (slowNorClosedThresholdEvents == Long.MAX_VALUE) {
            slowNorClosedThresholdEvents = 0;
        }
        if (thresholdForNorClosedThreshold * 1000L < l)
            slowNorClosedThresholdEvents++;
    }

    @ManagedAttribute
    public long getOpenSearchErrors() {
        return openSearchErrors;
    }

    public void updateOpenSearchErrors(long l) {
        if (openSearchErrors == Long.MAX_VALUE) {
            openSearchErrors = 0;
        }
        openSearchErrors += l;
    }

    @ManagedAttribute
    public long getOpenSearchWriteErrors() {
        return openSearchWriteErrors;
    }

    public void updateOpenSearchWriteErrors() {
        if (openSearchWriteErrors == Long.MAX_VALUE) {
            openSearchWriteErrors = 0;
        }
        openSearchWriteErrors++;
    }

    @ManagedAttribute
    public long getOpenSearchBatchInserts() {
        return openSearchBatchInserts;
    }

    public void updateOpenSearchBatchInserts() {
        if (openSearchBatchInserts == Long.MAX_VALUE) {
            openSearchBatchInserts = 0;
        }
        openSearchBatchInserts++;
    }

    @ManagedAttribute
    public long getOpenSearchUpdates() {
        return openSearchUpdates;
    }

    public void updateOpenSearchUpdates() {
        if (openSearchUpdates == Long.MAX_VALUE) {
            openSearchUpdates = 0;
        }
        openSearchUpdates++;
    }

    @ManagedAttribute
    public long getOpenSearchGetRequests() {
        return openSearchGetRequests;
    }

    public void updateOpenSearchGetRequests() {
        if (openSearchGetRequests == Long.MAX_VALUE) {
            openSearchGetRequests = 0;
        }
        openSearchGetRequests++;
    }

    @ManagedAttribute
    public long getIndexRequestQueueSize() {
        return scheduler.getIndexRequestQueueSize();
    }

    @ManagedAttribute
    public long getUpdateRequestQueueSize() {
        return scheduler.getUpdateRequestQueueSize();
    }

    @ManagedAttribute
    public long getDelayThresholdQueueSize() {
        return localCache.getDelayThresholdQueueSize();
    }

    @ManagedAttribute
    public long getDelayThresholdQueueMaxSize() {
        return localCache.getDelayThresholdQueueMaxSize();
    }

    @ManagedAttribute
    public long getInstanceKpiThresholdsMapSize() {
        return localCache.getInstanceKpiThresholdsConcurrentHashMapSize();
    }

    @ManagedAttribute
    public long getTransactionKpiThresholdsMapSize() {
        return localCache.getTransactionKpiThresholdsConcurrentHashMapSize();
    }

    @ManagedAttribute
    public long getInstanceKpiThresholdsMessages() {
        return instanceKpiThresholdsProcessed;
    }

    public void updateInstanceKpiThresholdsMessages() {
        if (instanceKpiThresholdsProcessed == Long.MAX_VALUE) {
            instanceKpiThresholdsProcessed = 0;
        }
        instanceKpiThresholdsProcessed++;
    }

    @ManagedAttribute
    public long getTransactionKpiThresholdsMessages() {
        return transactionKpiThresholdsProcessed;
    }

    public void updateTransactionKpiThresholdsMessages() {
        if (transactionKpiThresholdsProcessed == Long.MAX_VALUE) {
            transactionKpiThresholdsProcessed = 0;
        }
        transactionKpiThresholdsProcessed++;
    }

    @ManagedAttribute
    public long getOpenSearchMaxQueueSize() {
        return scheduler.getOpenSearchMaxQueueSize();
    }

    @ManagedAttribute
    public long getDuplicateNorThresholds() {
        return duplicateNorThresholds;
    }

    public void updateDuplicateNorThresholds() {
        if (duplicateNorThresholds == Long.MAX_VALUE) {
            duplicateNorThresholds = 0;
        }
        duplicateNorThresholds++;
    }

    @ManagedAttribute
    public long getOldNorThresholds() {
        return oldNorThresholds;
    }

    public void updateOldNorThresholds() {
        if (oldNorThresholds == Long.MAX_VALUE) {
            oldNorThresholds = 0;
        }
        oldNorThresholds++;
    }

    @ManagedAttribute
    public long getInstanceKpiThresholdsReceived() {
        return instanceKpiThresholdsReceived;
    }

    public void updateInstanceKpiThresholdsReceived(long value) {
        if (instanceKpiThresholdsReceived == Long.MAX_VALUE) {
            instanceKpiThresholdsReceived = 0;
        }
        instanceKpiThresholdsReceived += value;
    }

    @ManagedAttribute
    public long getTransactionKpiThresholdsReceived() {
        return transactionKpiThresholdsReceived;
    }

    public void updateTransactionKpiThresholdsReceived(long value) {
        if (transactionKpiThresholdsReceived == Long.MAX_VALUE) {
            transactionKpiThresholdsReceived = 0;
        }
        transactionKpiThresholdsReceived += value;
    }

    @ManagedAttribute
    public long getDelayQueueRemovedKeys() {
        return delayQueueRemovedKey;
    }

    public void updateDelayQueueRemovedKey() {
        if (delayQueueRemovedKey == Long.MAX_VALUE) {
            delayQueueRemovedKey = 0;
        }
        delayQueueRemovedKey++;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("Read Count AggregatedKpi Queue : ").append(getReadCountAggregatedKpiQueue()).append(", ");
        sb.append("Read Count ViolatedEvent Queue : ").append(getReadCountViolatedEventQueue()).append(", ");
        sb.append("Read Count NorThreshold Queue : ").append(getReadCountNorThresholdQueue()).append(", ");
        sb.append("Read Count NorClosedThreshold Queue : ").append(getReadCountNorClosedThresholdQueue()).append(", ");
        sb.append("Write Count AnomalySignal Queue : ").append(getWriteCountAnomalySignalQueue()).append(", ");
        sb.append("Write Count AnomalyAction Queue : ").append(getWriteCountAnomalyActionQueue()).append(", ");
        sb.append("Write Count Anomaly Messages Queue : ").append(getWriteCountAnomalyMessagesQueue()).append(", ");
        sb.append("Current Snapshots size : ").append(getSnapshots().size()).append(", ");
        sb.append("AggregatedKpi Queue Name : ").append(getReadAggregatedKpiQueueName()).append(", ");
        sb.append("ViolatedEvent Queue Name : ").append(getReadViolatedEventQueueName()).append(", ");
        sb.append("NorThreshold Queue Name : ").append(getReadNorThresholdQueueName()).append(", ");
        sb.append("NorClosedThreshold Queue Name : ").append(getReadNorClosedThresholdQueueName()).append(", ");
        sb.append("Anomaly Output Signal Queue Name : ").append(getWriteAnomalyOutputSignalQueueName()).append(", ");
        sb.append("Anomaly Output Action Queue Name : ").append(getWriteAnomalyOutputActionQueueName()).append(", ");
        sb.append("Violation Count : ").append(getViolationCount()).append(", ");
        sb.append("Anomaly Count : ").append(getAnomalyCount()).append(", ");
        sb.append("Redis Keys Not Found : ").append(getRedisKeysNotFound()).append(", ");
        sb.append("AggregatedKpi Processing Errors : ").append(getAggregatedKpiProcessingErrors()).append(", ");
        sb.append("ViolatedEvent Processing Errors : ").append(getViolatedEventProcessingErrors()).append(", ");
        sb.append("NorThreshold Processing Errors : ").append(getNorThresholdProcessingErrors()).append(", ");
        sb.append("NorClosedThreshold Processing Errors : ").append(getNorClosedThresholdProcessingErrors()).append(", ");
        sb.append("Rmq Read Errors : ").append(getRmqReadErrors()).append(", ");
        sb.append("Rmq Write Errors : ").append(getRmqWriteErrors()).append(", ");
        sb.append("Kpis Processed For AggregateKPIs : ").append(getKpisProcessedForAggregateKPIs()).append(", ");
        sb.append("Kpis Processed For ViolatedEvents : ").append(getKpisProcessedForViolatedEvents()).append(", ");
        sb.append("Kpis Processed For NorThreshold : ").append(getKpisProcessedForNorThreshold()).append(", ");
        sb.append("Kpis Processed For NorClosedThreshold : ").append(getKpisProcessedForNorClosedThreshold()).append(", ");
        sb.append("Min Process Time AggregatedKPI in ms: ").append(getMinProcessTimeAggregatedKPI()).append(", ");
        sb.append("Max Process Time AggregatedKPI in ms: ").append(getMaxProcessTimeAggregatedKPI()).append(", ");
        sb.append("Min Process Time ViolatedEvent in ms: ").append(getMinProcessTimeViolatedEvent()).append(", ");
        sb.append("Max Process Time ViolatedEvent in ms: ").append(getMaxProcessTimeViolatedEvent()).append(", ");
        sb.append("Min Process Time NorThreshold in ms: ").append(getMinTimeNorThreshold()).append(", ");
        sb.append("Max Process Time NorThreshold in ms: ").append(getMaxTimeNorThreshold()).append(", ");
        sb.append("Min Process Time NorClosedThreshold in ms: ").append(getMinTimeNorClosedThreshold()).append(", ");
        sb.append("Max Process Time NorClosedThreshold in ms: ").append(getMaxTimeNorClosedThreshold()).append(", ");
        sb.append("Slow Aggregated Kpi Events : ").append(getSlowAggregatedKpiEvents()).append(", ");
        sb.append("Slow Violated Events : ").append(getSlowViolatedEvents()).append(", ");
        sb.append("Slow NorThreshold Events : ").append(getSlowNorThresholdEvents()).append(", ");
        sb.append("OpenSearch Errors : ").append(getOpenSearchErrors()).append(", ");
        sb.append("OpenSearch Write Errors : ").append(getOpenSearchWriteErrors()).append(", ");
        sb.append("Max OpenSearch QueueSize : ").append(getOpenSearchMaxQueueSize()).append(", ");
        sb.append("Index Request QueueSize : ").append(getIndexRequestQueueSize()).append(", ");
        sb.append("OpenSearch Batch Inserts : ").append(getOpenSearchBatchInserts()).append(", ");
        sb.append("Update Request QueueSize : ").append(getUpdateRequestQueueSize()).append(", ");
        sb.append("OpenSearch Update Requests : ").append(getOpenSearchUpdates()).append(", ");
        sb.append("OpenSearch GET Requests : ").append(getOpenSearchGetRequests()).append(", ");
        sb.append("Instance Kpi thresholds received : ").append(getInstanceKpiThresholdsReceived()).append(", ");
        sb.append("Instance Kpi thresholds processed : ").append(getInstanceKpiThresholdsMessages()).append(", ");
        sb.append("Transaction Kpi thresholds received : ").append(getTransactionKpiThresholdsReceived()).append(", ");
        sb.append("Transaction Kpi thresholds processed : ").append(getTransactionKpiThresholdsMessages()).append(", ");
        sb.append("Duplicate NOR thresholds processed : ").append(getDuplicateNorThresholds()).append(", ");
        sb.append("Old NOR thresholds processed : ").append(getOldNorThresholds()).append(", ");
        sb.append("Delay threshold queue max size : ").append(getDelayThresholdQueueMaxSize()).append(", ");
        sb.append("Delay threshold queue size : ").append(getDelayThresholdQueueSize()).append(", ");
        sb.append("Delay threshold queue removed keys : ").append(getDelayQueueRemovedKeys()).append(", ");
        sb.append("Instance kpi threshold map size : ").append(getInstanceKpiThresholdsMapSize()).append(", ");
        sb.append("Transaction kpi threshold map size  : ").append(getTransactionKpiThresholdsMapSize());
        return sb.toString();
    }

}
