package com.heal.event.detector.utility;

public class Constants {

    private Constants() {
        //Dummy constructor to override implicit public constructor
    }

    //RabbitMQ specific
    public static final boolean IS_QUEUE_DURABLE = true;
    public static final boolean IS_QUEUE_EXCLUSIVE = false;
    public static final boolean QUEUE_AUTO_DELETE = false;

    public static final String COMMON_ATTRIBUTE = "ALL";

    public static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String LOWER_THRESHOLD = "Lower";
    public static final String UPPER_THRESHOLD = "Upper";
    public static final String CLUSTERS = "clusters";
    public static final String INSTANCES = "instances";

    public static final String DATA_SPLITTER_DEFAULT = "@#!#@";
    public static final int COMPONENT_AGENT_TYPE_ID = 1;
    public static final String TRANSACTION_COMPONENT_NAME = "Transaction";
}
