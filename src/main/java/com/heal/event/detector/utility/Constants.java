package com.heal.event.detector.utility;

public class Constants {

    private Constants() {
        //Dummy constructor to override implicit public constructor
    }

    //RabbitMQ specific
    public static final boolean IS_QUEUE_DURABLE = true;
    public static final boolean IS_QUEUE_EXCLUSIVE = false;
    public static final boolean QUEUE_AUTO_DELETE = false;

    public static final String COMMON_ATTRIBUTE = "ALL";

    public static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String LOWER_THRESHOLD = "Lower";
    public static final String UPPER_THRESHOLD = "Upper";
    public static final String CLUSTERS = "clusters";
    public static final String INSTANCES = "instances";

    public static final String DATA_SPLITTER_DEFAULT = "@#!#@";
    public static final int COMPONENT_AGENT_TYPE_ID = 1;
    public static final String TRANSACTION_COMPONENT_NAME = "Transaction";

    // Anomaly Management Constants
    public static final String ANOMALY_STATUS_OPEN = "Open";
    public static final String ANOMALY_STATUS_ONGOING = "Ongoing";
    public static final String ANOMALY_STATUS_CLOSED = "Close";
    public static final String ANOMALY_REDIS_KEY_PREFIX = "/anomalies/";
    public static final String ANOMALY_OPERATION_CREATE = "CREATE";
    public static final String ANOMALY_OPERATION_UPDATE = "UPDATE";
    public static final String ANOMALY_OPERATION_CLOSE = "CLOSE";

    // Alert Status Constants
    public static final String ALERT_STATUS_OPEN = "Open";
    public static final String ALERT_STATUS_UPDATE = "Update";
    public static final String ALERT_STATUS_CLOSE = "Close";
    public static final String ALERT_STATUS_REMAINDER = "Remainder";

    // Alert Type Constants
    public static final String ALERT_TYPE_PERSISTENCE = "Persistence";
    public static final String ALERT_TYPE_SUPPRESSION = "Suppression";

    // Severity Constants
    public static final String SEVERITY_LOW = "431";
    public static final String SEVERITY_WARN = "432";
    public static final String SEVERITY_CRITICAL = "433";

    // Entity Type Constants
    public static final String ENTITY_TYPE_TRANSACTION = "Transaction";
    public static final String ENTITY_TYPE_COMPONENT_INSTANCE = "ComponentInstance";
    public static final String ENTITY_TYPE_APPLICATION_INSTANCE = "ApplicationInstance";
    public static final String ENTITY_TYPE_SERVICE_INSTANCE = "ServiceInstance";
    public static final String ENTITY_TYPE_JOURNEY = "Journey";
}
