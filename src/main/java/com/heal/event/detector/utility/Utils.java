package com.heal.event.detector.utility;

import com.heal.configuration.enums.OperationType;
import com.heal.configuration.pojos.MaintenanceDetails;
import com.heal.event.detector.exception.EventDetectorException;
import com.heal.event.detector.pojos.enums.Intervals;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

@Slf4j
@Component
public class Utils {

    @Value("${offset.from.gmt:19800000}")
    Long offsetFromGMT;

    public static Timestamp getTimestamp(String timeInGMT, long offsetInMs) throws EventDetectorException {
        if (timeInGMT == null) {
            return null;
        }
        long epochInMilliSec = dateStrToLocalDateTime(timeInGMT).toInstant(ZoneOffset.UTC).toEpochMilli();
        epochInMilliSec = epochInMilliSec + offsetInMs;
        return new Timestamp(epochInMilliSec);
    }

    public static LocalDateTime dateStrToLocalDateTime(String dateStr) throws EventDetectorException {
        try {
            return LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern(Constants.DATE_FORMAT));
        } catch (DateTimeParseException e) {
            throw new EventDetectorException(e, "Exception While Parsing date");
        }
    }

    public static OperationType applyThreshold(Double kpiValue, String operationType, Map<String, Double> thresholds) {
        if (Objects.isNull(operationType)) {
            log.error("Invalid operation type, operation can not be null.");
            return null;
        }
        Double minVal = thresholds.get(Constants.LOWER_THRESHOLD);
        Double maxVal = thresholds.get(Constants.UPPER_THRESHOLD);
        try {
            OperationType opEnum = OperationType.fromString(operationType);
            if (opEnum == null) return null;
            switch (opEnum) {
                case LESSER_THAN:
                    if (minVal != null && Double.compare(kpiValue, minVal) < 0) {
                        return OperationType.LESSER_THAN;
                    }
                    break;
                case BETWEEN:
                    if (minVal != null && Double.compare(minVal, kpiValue) < 0
                            && (maxVal != null && Double.compare(kpiValue, maxVal) < 0)) {
                        return OperationType.BETWEEN;
                    }
                    break;
                case GREATER_THAN:
                    if (minVal != null && Double.compare(kpiValue, minVal) > 0) {
                        return OperationType.GREATER_THAN;
                    }
                    break;
                case NOT_EQUALS:
                    if (Double.compare(kpiValue, 1) != 0) {
                        return OperationType.NOT_EQUALS;
                    }
                    break;
                case NOT_BETWEEN:
                    if ((minVal != null && Double.compare(minVal, kpiValue) > 0)
                            || (maxVal != null && Double.compare(maxVal, kpiValue) < 0)) {
                        return OperationType.NOT_BETWEEN;
                    }
                    break;
                default:
                    return null;
            }
        } catch (Exception e) {
            log.error("Exception applied for applying thresholds, operation type: {}, thresholds:{}, value:{}", operationType, thresholds, kpiValue, e);
        }
        return null;
    }
    
    public static String getAnomalyScore(Map<String, Double> orderedThresholds, double kpiValue) {

        double upperThreshold = orderedThresholds.getOrDefault(Constants.UPPER_THRESHOLD, 0D);
        double lowerThreshold = orderedThresholds.getOrDefault(Constants.LOWER_THRESHOLD, 0D);
        double nearThreshold = getNearThreshold(kpiValue, upperThreshold, lowerThreshold);
        double anomalyScore = calculateAnomalyScore(kpiValue, nearThreshold, upperThreshold, lowerThreshold);
        return String.valueOf((double) Math.round(anomalyScore * 100) / 100);

    }

    public static Map<String, Double> getThresholdBasedOnOperationType(Map<String, Double> thresholds, String operationType) {
        Double minVal = thresholds.get(Constants.LOWER_THRESHOLD);
        try {
            OperationType opEnum = OperationType.fromString(operationType);
            if (opEnum == null) return null;
            if (opEnum.equals(OperationType.LESSER_THAN) || opEnum.equals(OperationType.GREATER_THAN)) {
                thresholds.put(Constants.UPPER_THRESHOLD, 0D);
                thresholds.put(Constants.LOWER_THRESHOLD, minVal);
            }
            return thresholds;
        } catch (IllegalArgumentException e) {
            log.error("Invalid operation type: " + operationType);
        }
        return new HashMap<>();
    }

    private static double getNearThreshold(double kpiValue, double upperThreshold, double lowerThreshold) {
        double numerator = Math.abs(kpiValue - upperThreshold);
        double denominator = Math.abs(kpiValue - lowerThreshold);
        return numerator <= denominator ? upperThreshold : lowerThreshold;
    }

    private static double calculateAnomalyScore(double kpiValue, double nearThreshold, double upperThreshold, double lowerThreshold) {
        double numerator = Math.abs(kpiValue - nearThreshold);
        double denominator = Math.abs(upperThreshold == lowerThreshold ? 1D : upperThreshold - lowerThreshold);
        return Math.tanh((2 * numerator) / denominator);
    }

    public static String getKey(String strDate, Intervals collationInterval) {
        // yyyy-MM-dd HH:mm:ss
        switch (collationInterval) {
            case Minutely:
                // Replacing last 2 chars (seconds) with 00 for collating per minute
                strDate = strDate.substring(0, strDate.length() - 2) + "00";
                break;
            case Hourly:
                strDate = strDate.substring(0, strDate.length() - 5) + "00:00";
                break;
            case Daily:
                strDate = strDate.substring(0, strDate.length() - 8) + "00:00:00";
                break;
            case Monthly:
                strDate = strDate.substring(0, strDate.length() - 11) + "01 00:00:00";
                break;
            case Yearly:
                strDate = strDate.substring(0, strDate.length() - 14) + "01-01 00:00:00";
                break;
            default:
                break;
        }
        return strDate;
    }

    public boolean isUnderMaintenance(String kpiCollectionTime, List<MaintenanceDetails> maintenanceDetailsList, long epochTime) {
        Timestamp timestamp;
        try {
            if (kpiCollectionTime == null) {
                timestamp = new Timestamp(epochTime);
            } else {
                timestamp = getTimestamp(kpiCollectionTime, offsetFromGMT);
            }
        } catch (EventDetectorException e) {
            log.error("Error while timestamp conversion", e);
            return false;
        }
        boolean isUnderMaintenance;
        for (MaintenanceDetails maintenanceDetails : maintenanceDetailsList) {
            if (maintenanceDetails == null) {
                log.error("Invalid maintenance details");
                continue;
            }

            log.debug("maintenanceDetails: {}", maintenanceDetails);

            Timestamp startTime = maintenanceDetails.getStartTime();
            Timestamp endTime = maintenanceDetails.getEndTime();

            if (startTime == null) {
                return false;
            }

            log.debug("kpi data collection time [{}], maintenance start time [{}], maintenance end time [{}]",
                    timestamp.getTime(), startTime.getTime(), endTime != null ? endTime.getTime() : null);

            if (endTime == null) {
                isUnderMaintenance = (startTime.before(timestamp) || startTime.equals(timestamp));
            } else {
                isUnderMaintenance = ((startTime.before(timestamp) || startTime.equals(timestamp))
                        && (endTime.after(timestamp) || endTime.equals(timestamp)));
            }

            if (isUnderMaintenance) return true;
        }
        return false;
    }

    public static boolean isOutOfOrder(String kpiViolationTime, int outOfOrderValue) {
        try {
            Calendar cal = Calendar.getInstance(TimeZone.getTimeZone("GMT"));
            long outOfOrderTime = cal.getTimeInMillis() - outOfOrderValue * 60000L;

            long violationTime = Utils.dateStrToLocalDateTime(kpiViolationTime).toInstant(ZoneOffset.UTC).toEpochMilli();
            return violationTime < outOfOrderTime;
        } catch (EventDetectorException e) {
            log.error("Error occurred while checking out of order data. violation time:{}, outOfOrderValue:{}", kpiViolationTime, outOfOrderValue, e);
            return true;
        }
    }

    public static String stringBuilderHelper(StringBuilder sb, String... strings) {
        sb.setLength(0);
        for (String key : strings) {
            sb.append(key);
            sb.append('_');
        }
        sb.setLength(sb.length() - 1);
        return sb.toString();
    }

    public static String getDateForOSIndex() {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy.MM");
        LocalDateTime now = LocalDateTime.now();
        return dtf.format(now);
    }
}