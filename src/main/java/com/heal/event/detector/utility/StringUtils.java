package com.heal.event.detector.utility;

import java.util.Collection;
import java.util.Objects;
import java.util.stream.Collectors;

public class StringUtils {
    public static boolean isEmpty(String s) {
        return (s == null || s.trim().isEmpty());
    }

    public static boolean isEmpty(Collection<?> subject) {
        if (subject == null || subject.isEmpty()) {
            return true;
        } else {
            return subject.stream().filter(Objects::nonNull).filter(c -> !c.toString().trim().isEmpty()).collect(Collectors.toSet()).isEmpty();
        }
    }

    public static long getLong(String number) {
        try {
            return Long.parseLong(number);
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    public static boolean isNumeric(String value) {
        if (isEmpty(value)) {
            return false;
        }
        /*This check looks for a Special constant called "Not a Number" which represents floating-point NaN value in the kpi value. If it's NaN, we fail the validation */
        if (value.equals("NaN")) {
            return false;
        }

        try {
            Double.parseDouble(value);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
