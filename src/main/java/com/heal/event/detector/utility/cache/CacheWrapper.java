package com.heal.event.detector.utility.cache;


import com.heal.configuration.entities.BasicInstanceBean;
import com.heal.configuration.pojos.*;
import com.heal.event.detector.repo.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Component
public class CacheWrapper {

    @Autowired
    RedisUtilities redisUtilities;
    @Autowired
    RedisLocalCache redisLocalCache;

    @Value("${redis.cache.mode:0}")
    private int mode;

    public Account getAccountDetails(String accountIdentifier) {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getAccountDetails(accountIdentifier);
            }
            return redisLocalCache.getAccountDetails(accountIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting account details for accountIdentifier : {}", accountIdentifier, e);
            return null;
        }
    }

    public List<TenantOpenSearchDetails> getTenantOpensearchDetails(String tenantIdentifier) {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getTenantOpenSearchDetails(tenantIdentifier);
            }
            return redisLocalCache.getTenantOpenSearchDetails(tenantIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting tenant opensearch mapping for tenant identifier {}", tenantIdentifier);
            return new ArrayList<>();
        }
    }

    public CompInstClusterDetails getInstanceDetails(String accountIdentifier, String instanceIdentifier) {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getInstanceDetails(accountIdentifier, instanceIdentifier);
            }
            return redisLocalCache.getInstanceDetails(accountIdentifier, instanceIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting instance details for accountIdentifier : {}, instance identifier : {}", accountIdentifier, instanceIdentifier, e);
            return null;
        }
    }

    public CompInstKpiEntity getInstanceKPIDetails(String accIdentifier, String instanceIdentifier, int kpiId) {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getInstanceKPIDetails(accIdentifier, instanceIdentifier, kpiId);
            }
            return redisLocalCache.getInstanceKPIDetails(accIdentifier, instanceIdentifier, kpiId);
        } catch (Exception e) {
            log.error("Error occurred while getting instance kpi details for accountIdentifier : {}, instance identifier : {}, kpiId : {}", accIdentifier, instanceIdentifier, kpiId, e);
            return null;
        }
    }

    public ComponentKpiEntity getComponentKPIDetails(String accIdentifier, String componentIdentifier, String kpiId) {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getComponentKPIDetails(accIdentifier, componentIdentifier, kpiId);
            }
            return redisLocalCache.getComponentKPIDetails(accIdentifier, componentIdentifier, kpiId);
        } catch (Exception e) {
            log.error("Error occurred while getting component kpi details for accountIdentifier : {}, componentIdentifier : {}, kpiId : {}", accIdentifier, componentIdentifier, kpiId, e);
            return null;
        }
    }

    public List<ViewTypes> getMstTypes() {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getMstTypes();
            }
            return redisLocalCache.getMstTypes();
        } catch (Exception e) {
            log.error("Error occurred while getting mst types", e);
            return Collections.emptyList();
        }
    }

    public Service getServiceDetails(String accIdentifier, String serviceIdentifier) {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getServiceDetails(accIdentifier, serviceIdentifier);
            }
            return redisLocalCache.getServiceDetails(accIdentifier, serviceIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting service details for accountIdentifier : {}, instance identifier : {}", accIdentifier, serviceIdentifier, e);
            return null;
        }
    }

    public KpiDetails getServiceKPIDetails(String accIdentifier, String srvIdentifier, int kpiId) {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getServiceKPIDetails(accIdentifier, srvIdentifier, kpiId);
            }
            return redisLocalCache.getServiceKPIDetails(accIdentifier, srvIdentifier, kpiId);
        } catch (Exception e) {
            log.error("Error occurred while getting service kpi details for accountIdentifier : {}, serviceIdentifier : {}, kpiId : {}", accIdentifier, srvIdentifier, kpiId, e);
            return null;
        }
    }

    public Transaction getTransactionDetails(String accIdentifier, String txn) {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getTransactionDetails(accIdentifier, txn);
            }
            return redisLocalCache.getTransactionDetails(accIdentifier, txn);
        } catch (Exception e) {
            log.error("Error occurred while getting transaction details for accountIdentifier : {}, transaction : {}", accIdentifier, txn, e);
            return null;
        }
    }

    public List<TxnKPIViolationConfig> getTransactionViolationConfigDetails(String accIdentifier, String txn) {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getTransactionViolationConfigDetails(accIdentifier, txn);
            }
            return redisLocalCache.getTransactionViolationConfigDetails(accIdentifier, txn);
        } catch (Exception e) {
            log.error("Error occurred while getting transaction violation config details for accountIdentifier : {}, transaction : {}", accIdentifier, txn, e);
            return Collections.emptyList();
        }
    }

    public List<CompInstKpiEntity> getInstanceKPIDetails(String accIdentifier, String instanceIdentifier) {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getInstanceKPIDetails(accIdentifier, instanceIdentifier);
            }
            return redisLocalCache.getInstanceKPIDetails(accIdentifier, instanceIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting instance kpi details for accountIdentifier : {}, instanceIdentifier : {}", accIdentifier, instanceIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<ComponentKpiEntity> getComponentKPIs(String accountIdentifier, String componentName) {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getComponentKPIs(accountIdentifier, componentName);
            }
            return redisLocalCache.getComponentKPIs(accountIdentifier, componentName);
        } catch (Exception e) {
            log.error("Error occurred while getting component kpi details for accountIdentifier : {}, component name : {}", accountIdentifier, componentName, e);
            return Collections.emptyList();
        }
    }

    public Application getApplicationByIdentifier(String accountIdentifier, String applicationIdentifier) {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getApplicationByIdentifier(accountIdentifier, applicationIdentifier);
            }
            return redisLocalCache.getApplicationByIdentifier(accountIdentifier, applicationIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting applications details for accountIdentifier : {}, application identifier : {}", accountIdentifier, applicationIdentifier, e);
            return null;
        }
    }

    public List<BasicEntity> getServicesMappedToApplication(String accountIdentifier, String applicationIdentifier) {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getServicesMappedToApplication(accountIdentifier, applicationIdentifier);
            }
            return redisLocalCache.getServicesMappedToApplication(accountIdentifier, applicationIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting application service details for accountIdentifier : {}, application identifier name : {}", accountIdentifier, applicationIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<BasicInstanceBean> getServiceInstances(String accountIdentifier, String serviceIdentifier) {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getServiceInstances(accountIdentifier, serviceIdentifier, true);
            }
            return redisLocalCache.getServiceInstances(accountIdentifier, serviceIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting service instances detail for accountIdentifier : {}, application identifier name : {}", accountIdentifier, serviceIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<BasicTransactionEntity> getServiceWiseTransaction(String accountIdentifier, String serviceIdentifier) {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getServiceWiseTransaction(accountIdentifier, serviceIdentifier);
            }
            return redisLocalCache.getServiceWiseTransaction(accountIdentifier, serviceIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting service wise transactions for accountIdentifier : {}, application identifier name : {}", accountIdentifier, serviceIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<BasicEntity> getServicesMappedToInstance(String accountIdentifier, String instanceIdentifier) {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getServicesMappedToInstance(accountIdentifier, instanceIdentifier);
            }
            return redisLocalCache.getServicesMappedToInstance(accountIdentifier, instanceIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting services mapped to instance for accountIdentifier : {}, instance identifier : {}", accountIdentifier, instanceIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<BasicEntity> getApplicationsMappedToService(String accountIdentifier, String serviceIdentifier) {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getApplicationsMappedToService(accountIdentifier, serviceIdentifier);
            }
            return redisLocalCache.getApplicationsMappedToService(accountIdentifier, serviceIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting applications mapped to service for accountIdentifier : {}, service identifier : {}", accountIdentifier, serviceIdentifier, e);
            return Collections.emptyList();
        }
    }

    public BasicEntity getServiceDetailsFromServiceId(String accountIdentifier, int serviceId) {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getServiceDetailsFromServiceId(accountIdentifier, serviceId);
            }
            return redisLocalCache.getServiceDetailsFromServiceId(accountIdentifier, serviceId);
        } catch (Exception e) {
            log.error("Error occurred while getting service details from service id for accountIdentifier : {}, service id : {}", accountIdentifier, serviceId, e);
            return null;
        }
    }

    public List<OSIndexZoneDetails> getHealIndexZones() {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getHealIndexZones();
            }
            return redisLocalCache.getHealIndexZones();
        } catch (Exception e) {
            log.error("Error occurred while getting heal opensearch index zone mapping. ", e);
            return null;
        }
    }

    public ApplicationSettings getApplicationSettings(String accountIdentifier, String applicationIdentifier) {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getApplicationSettings(accountIdentifier, applicationIdentifier);
            }
            return redisLocalCache.getApplicationSettings(accountIdentifier, applicationIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting application settings for accountIdentifier : {}, application identifier : {}", accountIdentifier, applicationIdentifier, e);
            return null;
        }
    }
}
