package com.heal.event.detector.utility.cache;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CacheConstants {

    public static final String ACCOUNT = "accountCache";
    public static final String INSTANCE = "instanceCache";
    public static final String INSTANCE_KPI = "instanceKpiCache";
    public static final String COMPONENT_KPI = "componentKpiCache";
    public static final String HEAL_TYPES = "healTypesCache";
    public static final String SERVICE = "service";
    public static final String SERVICE_KPI = "serviceKpiCache";
    public static final String TRANSACTIONS = "transactionsCache";
    public static final String TRANSACTIONS_VIOLATION = "transactionsViolationCache";
    public static final String INSTANCE_KPIS = "instanceKPIsCache";
    public static final String COMPONENT_KPIS = "componentKPIsCache";
    public static final String APPLICATIONS = "applicationsCache";
    public static final String APPLICATION_SERVICES = "applicationServicesCache";
    public static final String SERVICE_INSTANCES = "serviceInstancesCache";
    public static final String SERVICE_TRANSACTIONS = "serviceTransactionsCache";
    public static final String ANOMALY_SIGNAL = "anomalySignalCache";
}
