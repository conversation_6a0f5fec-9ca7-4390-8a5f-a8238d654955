package com.heal.event.detector.utility;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.TimeZone;

public class DateTimeUtil {

    public static String getTimeInGMT(long time) {
        DateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        return simpleDateFormat.format(time);
    }

    public static long computeTimeBeforeNDaysFromToTime(long days, long toTime){
        Instant instant = Instant.ofEpochMilli(toTime);
        return instant.minus(days, ChronoUnit.DAYS).toEpochMilli();
    }
}
