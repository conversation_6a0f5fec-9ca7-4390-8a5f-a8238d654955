package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.ViolatedEventProtos;
import com.heal.configuration.pojos.opensearch.InstanceKpiThresholds;
import com.heal.event.detector.repo.OpenSearchRepo;
import com.heal.event.detector.utility.Constants;
import com.heal.event.detector.utility.HealthMetrics;
import com.heal.event.detector.utility.LocalCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProcessInstanceKpiThresholdsData {

    @Autowired
    OpenSearchRepo openSearchRepo;
    @Autowired
    LocalCache localCache;

    @Autowired
    HealthMetrics metrics;

    @Async("ThreadPoolTaskExecutor")
    public void processInstanceKpiThreshold(String accountIdentifier, String applicationIdentifier, String thresholdType, ViolatedEventProtos.KpiInfo kpiThreshold, long violationEpochTime) {
        List<String> svcList = kpiThreshold.getSvcIdList();
        for (String serviceIdentifier : svcList) {
            InstanceKpiThresholds instanceKpiThresholds = getInstanceKpiThreshold(accountIdentifier, applicationIdentifier, serviceIdentifier, thresholdType, kpiThreshold, violationEpochTime);
            openSearchRepo.processInstanceKpiThreshold(instanceKpiThresholds, accountIdentifier);
        }

    }

    private InstanceKpiThresholds getInstanceKpiThreshold(String accountIdentifier, String applicationIdentifier, String serviceIdentifier,
                                                          String thresholdType, ViolatedEventProtos.KpiInfo instanceKpiThresholdProto, long violationEpochTime) {

        InstanceKpiThresholds instanceKpiThresholds = null;

        if (isValidThresholdDetails(instanceKpiThresholdProto.getOperationType(), instanceKpiThresholdProto.getThresholdsMap())) {

            String key = accountIdentifier + "#" + serviceIdentifier + "#" + instanceKpiThresholdProto.getInstanceId() + "#"
                    + instanceKpiThresholdProto.getKpiId() + "#" + instanceKpiThresholdProto.getKpiAttribute();

            InstanceKpiThresholds oldInstanceKpiThresholds = localCache.instanceKpiThresholdsConcurrentHashMap.getOrDefault(key, null);

            if (oldInstanceKpiThresholds != null) {
                log.trace("Existing instance kpi threshold details found in local cache for key {}", key);
                if (oldInstanceKpiThresholds.getStartTime() < violationEpochTime) {
                    if (!openSearchRepo.isThresholdChanged(oldInstanceKpiThresholds.getOperationType(), oldInstanceKpiThresholds.getThresholds(),
                            instanceKpiThresholdProto.getOperationType(), instanceKpiThresholdProto.getThresholdsMap())) {
                        log.warn("Duplicate kpiThreshold found with account {}, service {}, instance {}, kpi {}, kpiAttribute {} having same threshold. Skipping this data point.",
                                accountIdentifier, serviceIdentifier, instanceKpiThresholdProto.getInstanceId(), instanceKpiThresholdProto.getKpiId(), instanceKpiThresholdProto.getKpiAttribute());
                        metrics.updateDuplicateNorThresholds();
                        return null;
                    }
                } else {
                    log.warn("New instance kpi thresholds record has older start time that previously processed record. New record start time {}, Old record startTime {}", violationEpochTime, oldInstanceKpiThresholds.getStartTime());
                    metrics.updateOldNorThresholds();
                    return null;
                }
            }

            instanceKpiThresholds = InstanceKpiThresholds.builder()
                    .startTime(violationEpochTime)
                    .endTime(0L)
                    .instanceId(instanceKpiThresholdProto.getInstanceId())
                    .kpiAttribute(instanceKpiThresholdProto.getKpiAttribute())
                    .kpiId(Long.parseLong(instanceKpiThresholdProto.getKpiId()))
                    .serviceIdentifier(serviceIdentifier)
                    .applicationId(applicationIdentifier)
                    .thresholdType(thresholdType.toUpperCase())
                    .operationType(instanceKpiThresholdProto.getOperationType())
                    .thresholds(instanceKpiThresholdProto.getThresholdsMap())
                    .build();

        } else {
            log.error("Received incorrect NOR Threshold for [accountId:{}, applicationId:{}] " +
                            "for time[{}]. Hence ignoring below KPI NOR Threshold. {}\n\n",
                    accountIdentifier, applicationIdentifier, violationEpochTime, instanceKpiThresholdProto);
            metrics.updateNorThresholdProcessingErrors();
        }

        log.trace("Instance kpi threshold validated successfully. {}", instanceKpiThresholds);
        return instanceKpiThresholds;
    }

    private boolean isValidThresholdDetails(String operationType, Map<String, Double> thresholds) {
        Double minThreshold = thresholds.getOrDefault(Constants.LOWER_THRESHOLD, Double.NaN);
        Double maxThreshold = thresholds.getOrDefault(Constants.UPPER_THRESHOLD, Double.NaN);
        if (("in between".equalsIgnoreCase(operationType) || "not between".equalsIgnoreCase(operationType))
                && (minThreshold.isNaN() || maxThreshold.isNaN())) {
            log.error("Invalid instance kpi threshold details found. Operation type {}, thresholds {}.", operationType, thresholds);
            return false;
        } else if (("greater than".equalsIgnoreCase(operationType) || "lesser than".equalsIgnoreCase(operationType)) && minThreshold.isNaN()) {
            log.error("Invalid instance kpi threshold details found. Operation type {}, thresholds {}.", operationType, thresholds);
            return false;
        }
        log.trace("Valid instance kpi threshold details found. Operation type {}, thresholds {}.", operationType, thresholds);
        return true;
    }

}
