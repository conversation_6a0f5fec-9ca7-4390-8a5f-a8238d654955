package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.ViolatedEventProtos;
import com.heal.configuration.pojos.opensearch.TransactionKpiThresholds;
import com.heal.event.detector.repo.OpenSearchRepo;
import com.heal.event.detector.utility.Constants;
import com.heal.event.detector.utility.HealthMetrics;
import com.heal.event.detector.utility.LocalCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProcessTransactionKpiThresholdsData {
    @Autowired
    OpenSearchRepo openSearchRepo;
    @Autowired
    LocalCache localCache;

    @Autowired
    HealthMetrics metrics;

    @Async("ThreadPoolTaskExecutor")
    public void processTransactionKpiThreshold(String accountIdentifier, String applicationIdentifier, String thresholdType, ViolatedEventProtos.TransactionInfo txnThreshold, long violationEpochTime) {
        TransactionKpiThresholds transactionKpiThresholds = getTransactionKpiThreshold(accountIdentifier, applicationIdentifier, thresholdType, txnThreshold, violationEpochTime);
        openSearchRepo.processTransactionKpiThreshold(transactionKpiThresholds, accountIdentifier);
    }

    private TransactionKpiThresholds getTransactionKpiThreshold(String accountIdentifier, String applicationIdentifier, String thresholdType,
                                                                ViolatedEventProtos.TransactionInfo txnKpiThresholdProto, long violationEpochTime) {

        TransactionKpiThresholds transactionKpiThresholds = null;

        if (isValidThresholdDetails(txnKpiThresholdProto.getOperationType(), txnKpiThresholdProto.getThresholdsMap())) {

            String key = accountIdentifier + "#" + txnKpiThresholdProto.getTransactionId() + "#" + txnKpiThresholdProto.getKpiId() + "#"
                    + txnKpiThresholdProto.getResponseTimeType();
            TransactionKpiThresholds oldTransactionKpiThresholds = localCache.transactionKpiThresholdsConcurrentHashMap.getOrDefault(key, null);

            if (oldTransactionKpiThresholds != null) {
                log.trace("Existing transaction kpi threshold details found in local cache for key {}", key);
                if (oldTransactionKpiThresholds.getStartTime() < violationEpochTime) {
                    if (!openSearchRepo.isThresholdChanged(oldTransactionKpiThresholds.getOperationType(), oldTransactionKpiThresholds.getThresholds(),
                            txnKpiThresholdProto.getOperationType(), txnKpiThresholdProto.getThresholdsMap())) {
                        log.warn("Duplicate txnThreshold found with account {}, txn {}, kpi {}, responseTimeType {} having same threshold. Skipping this data point.",
                                accountIdentifier, txnKpiThresholdProto.getTransactionId(), txnKpiThresholdProto.getKpiId(), txnKpiThresholdProto.getResponseTimeType());
                        metrics.updateDuplicateNorThresholds();
                        return null;
                    }
                } else {
                    log.warn("New transaction kpi thresholds record has older start time that previously processed record. New record start time {}, Old record startTime {}", violationEpochTime, oldTransactionKpiThresholds.getStartTime());
                    metrics.updateOldNorThresholds();
                    return null;
                }
            }

            transactionKpiThresholds = TransactionKpiThresholds.builder()
                    .transactionId(txnKpiThresholdProto.getTransactionId())
                    .responseType(txnKpiThresholdProto.getResponseTimeType().name())
                    .kpiId(Long.parseLong(txnKpiThresholdProto.getKpiId()))
                    .startTime(violationEpochTime)
                    .endTime(0L)
                    .serviceIdentifier(txnKpiThresholdProto.getSvcId())
                    .applicationId(applicationIdentifier)
                    .thresholdType(thresholdType.toUpperCase())
                    .thresholds(txnKpiThresholdProto.getThresholdsMap())
                    .operationType(txnKpiThresholdProto.getOperationType())
                    .build();
        } else {
            log.error("Received incorrect NOR Threshold for [accountId:{}, applicationId:{}]" +
                            " for time[{}]. Hence ignoring below Transaction NOR Threshold. {}\n\n",
                    accountIdentifier, applicationIdentifier, violationEpochTime, txnKpiThresholdProto);
            metrics.updateNorThresholdProcessingErrors();
        }

        log.trace("Transaction kpi threshold validated successfully. {}", transactionKpiThresholds);
        return transactionKpiThresholds;
    }

    private boolean isValidThresholdDetails(String operationType, Map<String, Double> thresholds) {
        Double minThreshold = thresholds.getOrDefault(Constants.LOWER_THRESHOLD, Double.NaN);
        Double maxThreshold = thresholds.getOrDefault(Constants.UPPER_THRESHOLD, Double.NaN);
        if (("in between".equalsIgnoreCase(operationType) || "not between".equalsIgnoreCase(operationType))
                && (minThreshold.isNaN() || maxThreshold.isNaN())) {
            log.error("Invalid transaction kpi threshold details found. Operation type {}, thresholds {}.", operationType, thresholds);
            return false;
        } else if (("greater than".equalsIgnoreCase(operationType) || "lesser than".equalsIgnoreCase(operationType)) && minThreshold.isNaN()) {
            log.error("Invalid transaction kpi threshold details found. Operation type {}, thresholds {}.", operationType, thresholds);
            return false;
        }
        log.trace("Valid transaction kpi threshold details found. Operation type {}, thresholds {}.", operationType, thresholds);
        return true;
    }
}
