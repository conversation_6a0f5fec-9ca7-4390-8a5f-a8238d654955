package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.appnomic.appsone.common.protbuf.ViolatedEventProtos;
import com.heal.configuration.enums.AnomalyClosureReason;
import com.heal.configuration.enums.OperationType;
import com.heal.configuration.pojos.*;
import com.heal.configuration.protbuf.AnomalySummaryProtos;
import com.heal.event.detector.pojos.BasicKPIStateInfo;
import com.heal.event.detector.pojos.ViolatedData;
import com.heal.event.detector.pojos.enums.Intervals;
import com.heal.event.detector.pojos.enums.ViolationEventType;
import com.heal.event.detector.repo.RedisUtilities;
import com.heal.event.detector.utility.Constants;
import com.heal.event.detector.utility.StringUtils;
import com.heal.event.detector.utility.Utils;
import com.heal.event.detector.utility.cache.CacheWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class GetViolatedData {

    @Autowired
    RedisUtilities redisUtilities;
    @Autowired
    CacheWrapper cacheWrapper;
    @Autowired
    Utils utils;
    @Autowired
    PersistenceSuppression persistenceSuppression;

    @Value("${signal.severity.low:Default}")
    String lowSeveritySignal;

    @Value("${signal.severity.id.high:433}")
    String highSeverityIdSignal;

    @Value("${signal.severity.id.medium:432}")
    String mediumSeverityIdSignal;

    @Value("${signal.severity.id.low:431}")
    String lowSeverityIdSignal;

    @Value("${entity.type.instance:INSTANCE}")
    String entityTypeInstance;

    @Value("${entity.type.transaction:TRANSACTION}")
    String entityTypeTransaction;

    @Value("${entity.type.service:SERVICE}")
    String entityTypeService;

    @Value("${heal.transaction.component.identifier:Transaction}")
    private String transactionComponentIdentifier;

    @Value("${heal.batch.component.identifier:BatchProcess}")
    private String batchComponentIdentifier;

    @Value(("${heal.global.account.identifier:e573f852-5057-11e9-8fd2-b37b61e52317}"))
    private String globalAccountIdentifier;

    public List<ViolatedData> getViolatedDataObject(ViolatedEventProtos.ViolatedEvent violatedEventProto) {
        log.trace("ViolatedEvent in GetViolatedData: {}", violatedEventProto);

        List<ViolatedData> violatedDataList = new ArrayList<>();

        try {
            if (cacheWrapper.getAccountDetails(violatedEventProto.getAccountId()) == null) {
                log.error("Invalid account identifier [{}]", violatedEventProto.getAccountId());
                return Collections.emptyList();
            }

            if (!violatedEventProto.getThresholdType().equalsIgnoreCase("Static") && !violatedEventProto.getThresholdType().equalsIgnoreCase("RealTime")) {
                log.error("Received violated event has unsupported threshold type [{}]. Hence dropping the data point.",
                        violatedEventProto.getThresholdType());
                return null;
            }

            String timeInGMT = Utils.getKey(violatedEventProto.getViolationTmeInGMT(), Intervals.Minutely); //remove seconds
            long violatedTimeInEpochSec = Utils.dateStrToLocalDateTime(timeInGMT).toEpochSecond(ZoneOffset.UTC);

            log.debug("violatedEventProto.getViolationTmeInGMT(): {}, timeInGMT: {}, violatedTimeInEpochSec: {}",
                    violatedEventProto.getViolationTmeInGMT(), timeInGMT, violatedTimeInEpochSec);

            violatedDataList.addAll(getKpiViolatedData(violatedEventProto, timeInGMT, violatedTimeInEpochSec));
            violatedDataList.addAll(getTxnViolatedData(violatedEventProto, timeInGMT, violatedTimeInEpochSec));
            violatedDataList.addAll(getBatchJobViolatedData(violatedEventProto, timeInGMT, violatedTimeInEpochSec));
        } catch (Exception e) {
            log.error("Received incorrect date format, required date format[{}]. Hence ignoring below Violated Event\n{}\n", Constants.DATE_FORMAT, violatedEventProto, e);
        }
        return violatedDataList;
    }

    /**
     * Creates or Updates ViolationDetails for an event and handles closing of anomalies if required.
     * @param accountIdentifier
     * @param instanceIdentifier
     * @param kpiId
     * @param serviceIds
     * @param kv
     * @param thresholds
     * @param serviceIdentifier
     * @param violationLevel
     * @param opType
     * @param kpiAttributeName
     * @param eventTimeInEpochSec
     * @param violationFor
     * @param isTxn Boolean flag to indicate if the event is for a transaction.
     * @param transactionId
     */
    public void handleViolationDetails(String accountIdentifier, String instanceIdentifier, String kpiId,
                                       List<String> serviceIds, KpiViolationConfig kv, Map<String, Double> thresholds, String serviceIdentifier,
                                       String violationLevel, OperationType opType, String kpiAttributeName, long eventTimeInEpochSec,
                                       String violationFor, boolean isTxn, String transactionId) {

        String entityType = isTxn ? entityTypeTransaction : entityTypeInstance;
        String entityIdentifier = isTxn ? transactionId : instanceIdentifier;
        if (!isTxn && transactionId != null) {
            log.error("Transaction ID is not expected for instance violation.");
            transactionId = null;
        }

        ViolationDetails violationDetails = redisUtilities.getViolationDetails(
                accountIdentifier,
                instanceIdentifier,
                kpiId,
                kpiAttributeName,
                violationFor,
                transactionId);

        boolean resetViolationStatusFlag = false;
        AnomalyClosureReason reason = null;
        String severityId = String.valueOf(kv.getThresholdSeverityId());

        if (violationDetails != null) {
            // Ensure ViolationStatus exists for this severity
            if (!violationDetails.getViolationStatusMap().containsKey(severityId)) {
                violationDetails.getViolationStatusMap().put(severityId, buildViolationStatus(kv, thresholds));
            }

            // Check persistence/suppression config
            boolean persSuppValid = isPersistenceSuppressionValid(violationDetails, kv, entityType, accountIdentifier, instanceIdentifier, kpiId, serviceIds, serviceIdentifier, severityId);
            if (!persSuppValid) {
                log.warn("Persistence/suppression config invalid for entityType: {}, accountIdentifier: {}, instanceIdentifier: {}, kpiId: {}, serviceIds: {}, severityId: {}", entityType, accountIdentifier, instanceIdentifier, kpiId, serviceIds, severityId);
                resetViolationStatusFlag = true;
                reason = AnomalyClosureReason.MISSING_MISMATCHED_PERSISTENCE_SUPPRESSION;
            }

            // Check thresholds/operation name
            boolean thresholdsValid = violationDetails.checkThresholdsOperationNameMatchForSeverity(severityId, kv.getOperation(), thresholds);
            if (!thresholdsValid) {
                log.warn("Operation name and thresholds mismatch for severity: {} (kpiId: {}, instanceIdentifier: {})", severityId, kpiId, instanceIdentifier);
                resetViolationStatusFlag = true;
                reason = AnomalyClosureReason.MISMATCHED_OPERATION_NAME_THRESHOLDS;
            }

            if (Objects.isNull(opType)) {
                violationDetails.resetViolationStatusBySeverity(severityId);
                redisUtilities.putViolationDetails(accountIdentifier, instanceIdentifier, kpiId, kpiAttributeName,
                        violationFor, transactionId, violationDetails);
                return;
            }
            violationDetails.updateViolationCountBySeverity(severityId, eventTimeInEpochSec);
            redisUtilities.putViolationDetails(accountIdentifier, instanceIdentifier, kpiId, kpiAttributeName,
                    violationFor, transactionId, violationDetails);
        } else {
            // If violationDetails is null, create new
            int persistence = kv.getPersistence();
            int suppression = kv.getSuppression();
            if (persistence <= 0 || suppression <= 0) {
                String serviceId = serviceIdentifier == null ? serviceIds.get(0) : serviceIdentifier;
                Map<String, Integer> persSupp = persistenceSuppression.getPersSuppAndCollectionIntervalAtServiceConf(
                        entityType,
                        accountIdentifier,
                        instanceIdentifier,
                        kpiId,
                        serviceId,
                        severityId);
                if (persSupp != null && persSupp.containsKey("persistence") && persSupp.containsKey("suppression")) {
                    persistence = persSupp.get("persistence");
                    suppression = persSupp.get("suppression");
                } else {
                    log.warn("violationDetails not created as persistence suppression configuration not found for entityType: {}, accountIdentifier: {}, instanceIdentifier: {}, kpiId: {}, serviceId: {}, severityId: {}", entityType, accountIdentifier, instanceIdentifier, kpiId, serviceId, severityId);
                    return;
                }
            }
            ViolationStatus violationStatus = ViolationStatus.builder()
                    .persistence(persistence)
                    .suppression(suppression)
                    .thresholds(thresholds)
                    .operationName(kv.getOperation())
                    .build();
            Map<String, ViolationStatus> violationStatusMap = new HashMap<>();
            violationStatusMap.put(severityId, violationStatus);
            violationDetails = ViolationDetails.builder()
                    .violationStatusMap(violationStatusMap)
                    .violationLevel(violationLevel)
                    .highestViolatedSeverity(severityId)
                    .serviceList(serviceIdentifier == null ? serviceIds : Collections.singletonList(serviceIdentifier))
                    .build();
            violationDetails.updateViolationCountBySeverity(severityId, eventTimeInEpochSec);
            redisUtilities.putViolationDetails(accountIdentifier, instanceIdentifier, kpiId, kpiAttributeName,
                    violationFor, transactionId, violationDetails);
        }

        // At the end, handle reset and anomaly close if needed
        if (resetViolationStatusFlag) {
            handleResetAndCloseAnomaly(violationDetails, accountIdentifier, instanceIdentifier, kpiId, kpiAttributeName, violationFor, transactionId, entityType, entityIdentifier, reason.toString());
        }
    }

    // Helper: Build ViolationStatus
    private ViolationStatus buildViolationStatus(KpiViolationConfig kv, Map<String, Double> thresholds) {
        return ViolationStatus.builder()
                .persistence(kv.getPersistence())
                .suppression(kv.getSuppression())
                .thresholds(thresholds)
                .operationName(kv.getOperation())
                .build();
    }

    // Helper: Validate persistence/suppression config
    private boolean isPersistenceSuppressionValid(ViolationDetails violationDetails, KpiViolationConfig kv, String entityType, String accountIdentifier, String instanceIdentifier, String kpiId, List<String> serviceIds, String serviceIdentifier, String severityId) {
        int persistence = kv.getPersistence();
        int suppression = kv.getSuppression();
        if (persistence > 0 && suppression > 0) {
            return violationDetails.checkPersistenceSuppressionMatchForSeverity(severityId, persistence, suppression);
        }
        String serviceId = serviceIdentifier == null ? serviceIds.get(0) : serviceIdentifier;
        Map<String, Integer> persSupp = persistenceSuppression.getPersSuppAndCollectionIntervalAtServiceConf(
                entityType,
                accountIdentifier,
                instanceIdentifier,
                kpiId,
                serviceId,
                violationDetails.getHighestViolatedSeverity());
        if (persSupp == null) {
            return false;
        }
        if (!persSupp.containsKey("persistence") || !persSupp.containsKey("suppression")) {
            return false;
        }
        return violationDetails.checkPersistenceSuppressionMatchForSeverity(severityId, persSupp.get("persistence"), persSupp.get("suppression"));
    }

    // Helper: Handle reset and anomaly close
    private void handleResetAndCloseAnomaly(ViolationDetails violationDetails, String accountIdentifier, String instanceIdentifier, String kpiId, String kpiAttributeName, String violationFor, String transactionId, String entityType, String entityIdentifier, String reason) {
        if (violationDetails.getAnomalyEventStatus() != null && violationDetails.getAnomalyEventStatus().getAnomalyEventId() != null) {
            redisUtilities.deleteViolationDetails(accountIdentifier, instanceIdentifier, kpiId, kpiAttributeName,
                    violationFor, transactionId);
            AnomalySummaryProtos.AnomalySummary anomalySummary = AnomalySummaryProtos.AnomalySummary.newBuilder()
                    .setAccountIdentifier(accountIdentifier)
                    .setClosingReason(reason)
                    .setKpiId(kpiId)
                    .setViolationFor(violationFor)
                    .setEntityType(entityType)
                    .setEntityIdentifier(entityIdentifier)
                    .setKpiAttribute(kpiAttributeName)
                    .build();
//            anomalyManagementService.closeAnomaly(anomalySummary);
        } else {
            violationDetails.resetViolationCounts();
            redisUtilities.putViolationDetails(accountIdentifier, instanceIdentifier, kpiId, kpiAttributeName,
                    violationFor, transactionId, violationDetails);
        }
    }

    /**
     * Filters the violated data list to return only those with the highest
     * severity.
     * If no violations are found, it checks if an anomaly event exists and updates
     * the closing window count.
     * If the closing window count matches the configured closing window, it closes
     * the anomaly.
     *
     * @param accountIdentifier
     * @param instanceIdentifier
     * @param kpiId
     * @param serviceIds
     * @param violatedDataList
     * @param kpiAttributeName
     * @param serviceIdentifier
     * @param eventTimeInEpochSec
     * @param violationFor
     * @param isTxn
     * @param transactionId
     * @return List of ViolatedData with the highest severity or empty if no violations
     *         found.
     */
    public List<ViolatedData> getViolatedDataForHighestSeverity(String accountIdentifier, String instanceIdentifier,
                                                                String kpiId, List<String> serviceIds, List<ViolatedData> violatedDataList, String kpiAttributeName,
                                                                String serviceIdentifier, long eventTimeInEpochSec, String violationFor, boolean isTxn, String transactionId) {
        String entityType = isTxn ? entityTypeTransaction : entityTypeInstance;
        String entityIdentifier = isTxn ? transactionId : instanceIdentifier;
        if (!isTxn && transactionId != null) {
            log.error("Transaction ID is not expected for instance violation.");
            transactionId = null;
        }
        ViolationDetails violationDetails = redisUtilities.getViolationDetails(accountIdentifier, instanceIdentifier,
                kpiId, kpiAttributeName, violationFor, transactionId);

        if (violationDetails == null) {
            return violatedDataList;
        }

        if (!violatedDataList.isEmpty()) {
            // Set highest violated severity based on available violation counts
            if (hasSeverityViolation(violationDetails, highSeverityIdSignal)
                    && persistenceSuppression.persistenceSuppressionMetForSeverity(violationDetails, highSeverityIdSignal)) {
                violationDetails.setHighestViolatedSeverity(highSeverityIdSignal);
            } else if (hasSeverityViolation(violationDetails, mediumSeverityIdSignal)
                    && persistenceSuppression.persistenceSuppressionMetForSeverity(violationDetails, mediumSeverityIdSignal)) {
                violationDetails.setHighestViolatedSeverity(mediumSeverityIdSignal);
            } else if (hasSeverityViolation(violationDetails, lowSeverityIdSignal)
                    && persistenceSuppression.persistenceSuppressionMetForSeverity(violationDetails, lowSeverityIdSignal)) {
                violationDetails.setHighestViolatedSeverity(lowSeverityIdSignal);
            } else {
                log.error("No valid violation severity found for KPI [{}] mapped to instance [{}] and account [{}]. Hence dropping the data point.", kpiId, instanceIdentifier, accountIdentifier);
                violationDetails.setHighestViolatedSeverity(null);
            }
            redisUtilities.putViolationDetails(accountIdentifier, instanceIdentifier, kpiId, kpiAttributeName,
                    violationFor, transactionId, violationDetails);
            // Filter to only highest severity
            return violatedDataList.stream()
                    .filter(vd -> vd.getThresholdSeverity().equals(violationDetails.getHighestViolatedSeverity()))
                    .collect(Collectors.toList());
        }

        // No violations, but violationDetails exists
        if (violationDetails.getAnomalyEventStatus() != null
                && violationDetails.getAnomalyEventStatus().getAnomalyEventId() != null) {
            violationDetails.updateClosingWindowCount(eventTimeInEpochSec);
            String serviceId = serviceIdentifier == null ? serviceIds.get(0) : serviceIdentifier;
            Map<String, Integer> collInt = persistenceSuppression.getPersSuppAndCollectionIntervalAtServiceConf(
                    entityType,
                    accountIdentifier,
                    instanceIdentifier,
                    kpiId,
                    serviceId,
                    violationDetails.getHighestViolatedSeverity());
            ServiceConfiguration serviceConfiguration = persistenceSuppression
                    .getPersistenceSuppressionServiceConf(accountIdentifier, serviceId, collInt.get("collectionInterval"));
            if (serviceConfiguration == null) {
                log.warn("Unable to update ClosingWindowCount as Persistence suppression conf unavailable for account [{}], instance [{}], " +
                                "kpiId [{}], collection interval [{}], and services [{}]", accountIdentifier,
                        instanceIdentifier, kpiId, collInt.get("collectionInterval"), serviceId);
                return Collections.emptyList();
            }

            if (violationDetails.getClosingWindowCount() == serviceConfiguration.getAnomalyConfiguration().getClosingWindow()) {
                handleResetAndCloseAnomaly(violationDetails, accountIdentifier, instanceIdentifier, kpiId, kpiAttributeName, violationFor, transactionId, entityType, entityIdentifier, String.valueOf(AnomalyClosureReason.CLOSING_WINDOW_COUNT_REACHED));
            } else {
                redisUtilities.putViolationDetails(accountIdentifier, instanceIdentifier, kpiId, kpiAttributeName,
                        violationFor, transactionId, violationDetails);
            }
        } else {
            redisUtilities.deleteViolationDetails(accountIdentifier, instanceIdentifier, kpiId,
                    kpiAttributeName, violationFor, transactionId);
        }
        return Collections.emptyList();
    }

    // Helper: Check if a severity has a violation count > 0
    private boolean hasSeverityViolation(ViolationDetails violationDetails, String severityId) {
        return violationDetails.getViolationStatusMap().containsKey(severityId)
                && violationDetails.getViolationStatusMap().get(severityId).getViolationCount() > 0;
    }

    /**
     * This method checks the violations for the given violated data and returns a list of ViolatedData
     * with the highest severity based on the configured thresholds.
     *
     * @param violatedData The violated data to check for violations.
     * @return List of ViolatedData with the highest severity.
     */
    public List<ViolatedData> checkViolationsGetHighestSeverity(ViolatedData violatedData) {
        // Fetch violation configs based on event type and violation level
        List<KpiViolationConfig> violationConfigList = getViolationConfigsForViolatedData(violatedData);
        if (violationConfigList == null) return null;

        boolean isTxn = violatedData.getEventType().equals(ViolationEventType.TXN_VIOLATION);

        List<ViolatedData> violatedDataList = violationConfigList.stream()
                .filter(kv -> isValidViolationConfig(kv, violatedData))
                .map(kv -> mapConfigToViolatedData(kv, violatedData, isTxn))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // Only keep highest severity
        return getViolatedDataForHighestSeverity(
                violatedData.getAccountId(),
                violatedData.getInstanceId(),
                violatedData.getKpiId(),
                violatedData.getServiceList(),
                violatedDataList,
                violatedData.getKpiAttribute(),
                violatedData.getMetaData().get("serviceIdentifier"),
                violatedData.getViolationTime(),
                violatedData.getViolationFor(),
                isTxn,
                violatedData.getTransactionId()
        );
    }

    // Helper: Fetch violation configs for ViolatedData
    private List<KpiViolationConfig> getViolationConfigsForViolatedData(ViolatedData violatedData) {
        try {
            if (violatedData.getEventType().equals(ViolationEventType.KPI_VIOLATION)) {
                if (violatedData.getMetaData().get("violationLevel").equals(entityTypeInstance)) {
                    CompInstKpiEntity kpiDetails = cacheWrapper.getInstanceKPIDetails(violatedData.getAccountId(),
                            violatedData.getInstanceId(), Integer.parseInt(violatedData.getKpiId()));
                    if (kpiDetails == null) {
                        log.error("Instance KPI details not found for accountId: {}, instanceId: {}, kpiId: {}",
                                violatedData.getAccountId(), violatedData.getInstanceId(), violatedData.getKpiId());
                        return null;
                    }
                    return kpiDetails.getKpiViolationConfig().get(Constants.COMMON_ATTRIBUTE);
                } else if ((violatedData.getMetaData().get("violationLevel").equals(entityTypeService))
                        && violatedData.getMetaData().get("serviceIdentifier") != null) {
                    KpiDetails serviceKpiDetails = cacheWrapper.getServiceKPIDetails(violatedData.getAccountId(),
                            violatedData.getMetaData().get("serviceIdentifier"),
                            Integer.parseInt(violatedData.getKpiId()));
                    if (serviceKpiDetails == null) {
                        log.error("Service KPI details not found for accountId: {}, serviceIdentifier: {}, kpiId: {}",
                                violatedData.getAccountId(), violatedData.getMetaData().get("serviceIdentifier"),
                                violatedData.getKpiId());
                        return null;
                    }
                    return serviceKpiDetails.getKpiViolationConfig().get(violatedData.getKpiAttribute());
                } else {
                    log.debug("KPI violation level is not INSTANCE or SERVICE. Hence dropping the data point for accountId: {}, instanceId: {}, kpiId: {}",
                            violatedData.getAccountId(), violatedData.getInstanceId(), violatedData.getKpiId());
                    return null;
                }
            } else if (violatedData.getEventType().equals(ViolationEventType.TXN_VIOLATION)) {
                ComponentKpiEntity componentKPIDetails = cacheWrapper.getComponentKPIDetails(
                        violatedData.getAccountId(), transactionComponentIdentifier, violatedData.getKpiId());
                if (componentKPIDetails == null) {
                    log.error("Transaction KPI details not found for accountId: {}, kpiId: {}",
                            violatedData.getAccountId(), violatedData.getKpiId());
                    return null;
                }
                List<TxnKPIViolationConfig> txnKPIViolationConfigList = cacheWrapper
                        .getTransactionViolationConfigDetails(violatedData.getAccountId(),
                                violatedData.getTransactionId());
                return txnKPIViolationConfigList.stream()
                        .map(TxnKPIViolationConfig::getKpiViolationConfig)
                        .flatMap(Collection::parallelStream)
                        .filter(c -> c.getKpiId() == Integer.parseInt(violatedData.getKpiId()))
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("Error while finding kpi details : Account:{}, Instance:{}, kpiId:{}",
                    violatedData.getAccountId(), violatedData.getInstanceId(), violatedData.getKpiId(), e);
            return null;
        }
        return null;
    }

    // Helper: Validate a violation config
    private boolean isValidViolationConfig(KpiViolationConfig kv, ViolatedData violatedData) {
        if (kv.getStatus() == 0) {
            log.info("Static violation configuration is disabled for attribute [{}] of Kpi [{}] mapped to instance [{}] and account [{}]. Dropping the data point.",
                    violatedData.getKpiAttribute(), violatedData.getKpiId(),
                    violatedData.getInstanceId(), violatedData.getAccountId());
            return false;
        }
        if (kv.getMaxThreshold() == null || kv.getMinThreshold() == null || kv.getOperation() == null) {
            log.error("Threshold data not found for attribute [{}] of Kpi [{}] mapped to instance [{}] and account [{}]. Dropping the data point.",
                    violatedData.getKpiAttribute(), violatedData.getKpiId(),
                    violatedData.getInstanceId(), violatedData.getAccountId());
            return false;
        }
        return true;
    }

    // Helper: Map KpiViolationConfig to ViolatedData (with handleViolationDetails call)
    private ViolatedData mapConfigToViolatedData(KpiViolationConfig kv, ViolatedData violatedData, boolean isTxn) {
        ViolatedData violatedDataNew = new ViolatedData(violatedData);
        Map<String, Double> thresholds = new HashMap<>();
        thresholds.put(Constants.LOWER_THRESHOLD, kv.getMinThreshold());
        thresholds.put(Constants.UPPER_THRESHOLD, kv.getMaxThreshold());

        if (violatedDataNew.getValue() == null) {
            log.error("ViolatedData value is null for Kpi [{}] - skipping config.",
                    violatedDataNew.getKpiId());
            return null;
        }
        OperationType opType = Utils.applyThreshold(Double.valueOf(violatedDataNew.getValue()),
                kv.getOperation(), thresholds);

        log.debug("Valid violation configuration found for attribute [{}] of KpiId [{}], opType:{} mapped to instance [{}] and accountId [{}]",
                violatedDataNew.getKpiAttribute(), violatedDataNew.getKpiId(), opType,
                violatedDataNew.getInstanceId(), violatedDataNew.getAccountId());

        if (Objects.isNull(opType)) {
            // If the operation type is null, it means the value does not violate any thresholds.
            return null;
        }

        violatedDataNew.setThresholdSeverity(String.valueOf(kv.getThresholdSeverityId()));
        violatedDataNew.setOperationType(opType.getOperationType());
        Map<String, Double> orderedThreshold = new HashMap<>();
        // If the KPI type is Core or Availability, we need to order the thresholds based on the operation type.
        if (violatedDataNew.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core
                || violatedDataNew.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Availability) {
            orderedThreshold = Utils.getThresholdBasedOnOperationType(thresholds, opType.getOperationType());
            if (orderedThreshold == null || orderedThreshold.isEmpty()) {
                violatedDataNew.setThresholds(thresholds);
            } else {
                violatedDataNew.setThresholds(orderedThreshold);
            }
        } else {
            violatedDataNew.setThresholds(thresholds);
        }

        Map<String, String> allMetaData = violatedDataNew.getMetaData();
        if (violatedDataNew.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core) {
            allMetaData.put("anomalyScore", Utils.getAnomalyScore(thresholds, Double.parseDouble(violatedDataNew.getValue())));
        } else if (violatedDataNew.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Availability) {
            if (violatedDataNew.getThresholdSeverity().equals(highSeverityIdSignal)) {
                allMetaData.put("anomalyScore", "1");
            } else if (violatedDataNew.getThresholdSeverity().equals(mediumSeverityIdSignal)) {
                allMetaData.put("anomalyScore", "0.7");
            } else if (violatedDataNew.getThresholdSeverity().equals(lowSeverityIdSignal)) {
                allMetaData.put("anomalyScore", "0.5");
            }
        }
        violatedDataNew.setMetaData(allMetaData);

        handleViolationDetails(violatedDataNew.getAccountId(),
                violatedDataNew.getInstanceId(), violatedDataNew.getKpiId(),
                violatedDataNew.getServiceList(), kv, thresholds,
                violatedDataNew.getMetaData().get("serviceIdentifier"),
                violatedDataNew.getMetaData().get("violationLevel"), opType,
                violatedDataNew.getKpiAttribute(), violatedDataNew.getViolationTime(),
                violatedData.getViolationFor(), isTxn, violatedData.getTransactionId());

        return violatedDataNew;
    }

    private List<ViolatedData> getKpiViolatedData(ViolatedEventProtos.ViolatedEvent violatedEventProto, String timeInGMT, long violatedTimeInEpochSec) {
        String accId = violatedEventProto.getAccountId();

        return violatedEventProto.getKpisList().parallelStream()
                .filter(c -> {
                    if (!instanceValidation(accId, c.getKpiInfo().getInstanceId())
                            || !serviceValidation(accId, c.getKpiInfo().getSvcIdList())) {
                        log.error("Either instance or/and service related details unavailable for violated event proto [{}]",
                                violatedEventProto);
                        return false;
                    }
                    return true;
                })
                .flatMap(kpi -> {
                    BasicKPIStateInfo kpiStateInfo = getKpiBasicInfo(violatedEventProto, kpi);
                    if (kpiStateInfo == null) {
                        log.info("Kpi violation is ignored. Reason: Invalid KpiInfo. KPI details: [{}]", kpi);
                        return null;
                    }
                    if (!kpiStateInfo.getGenerateAnomaly()) {
                        log.info("Kpi violation is ignored. Reason: Generate Anomaly is set to false. KPI details: [{}]", kpi);
                        return null;
                    }

                    List<String> validServices = kpi.getKpiInfo().getSvcIdList();
                    if (!kpiStateInfo.getIsMaintenanceExcluded()) {
                        if (isInstanceMaintenanceWindowOn(kpi.getKpiInfo().getInstanceId(), violatedEventProto.getAccountId(), violatedEventProto.getViolationTmeInGMT())) {
                            log.info("Kpi violation is ignored. Reason: Instance is under maintenance. KPI details: [{}]", kpi);
                            return null;
                        }

                        validServices = getSvsNotUnderMaintenanceWindow(kpi.getKpiInfo().getSvcIdList(), violatedEventProto.getAccountId(), violatedEventProto.getViolationTmeInGMT());
                        if (validServices.isEmpty()) {
                            log.warn("Kpi violation is ignored. Reason: Service(s) is under maintenance. Time:{}, KPI details:[{}]", violatedEventProto.getViolationTmeInGMT(), kpi);
                            return null;
                        }
                    }

                    ViolatedEventProtos.KpiInfo kpiInfo = kpi.getKpiInfo();

                    ViolatedData violatedData = getViolatedData(violatedEventProto, violatedTimeInEpochSec);
                    violatedData.setServiceList(new ArrayList<>(validServices));
                    violatedData.setValue(kpi.getValue());
                    violatedData.setEventType(ViolationEventType.KPI_VIOLATION);
                    violatedData.setKpiId(kpiInfo.getKpiId());
                    violatedData.setInstanceId(kpiInfo.getInstanceId());
                    violatedData.setKpiAttribute(kpiInfo.getKpiAttribute());
                    violatedData.setOperationType(kpiInfo.getOperationType());
                    violatedData.setThresholds(kpiInfo.getThresholdsMap());
                    violatedData.setThresholdSeverity(StringUtils.isEmpty(kpiInfo.getThresholdSeverity()) ? lowSeverityIdSignal : kpiInfo.getThresholdSeverity());
                    violatedData.setKpiType(kpiStateInfo.getKpiType());
                    violatedData.setDisplayAttributeName(kpiStateInfo.getAttributeDisplayName());
                    violatedData.setIsInfo(kpiStateInfo.getIsInfo());
                    violatedData.setMaintenanceExcluded(kpiStateInfo.getIsMaintenanceExcluded());
                    violatedData.setKpiViolationTime(timeInGMT);

                    Map<String, String> metaDataMap = new HashMap<>(kpiInfo.getMetaDataMap());
                    if (!StringUtils.isEmpty(kpiInfo.getAgentId())) {
                        metaDataMap.put("agentUid", kpiInfo.getAgentId());
                    }
                    violatedData.setMetaData(metaDataMap);

                    log.debug("KPI violatedData [{}]", violatedData);

                    List<ViolatedData> violatedDataList = checkViolationsGetHighestSeverity(violatedData);

                    return violatedDataList != null ? violatedDataList.stream() : Stream.empty();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<ViolatedData> getTxnViolatedData(ViolatedEventProtos.ViolatedEvent violatedEventProto, String timeInGMT, long violatedTimeInEpochSec) {
        String accId = violatedEventProto.getAccountId();

        return violatedEventProto.getTransactionsList().parallelStream().filter(c -> {
                    if (!serviceValidation(accId, Collections.singletonList(c.getTxnInfo().getSvcId()))
                            || cacheWrapper.getTransactionDetails(accId, c.getTxnInfo().getTransactionId()) == null) {
                        log.error("Transaction or/and service related details unavailable for violated event proto [{}]",
                                violatedEventProto);
                        return false;
                    }
                    return true;
                }).flatMap(txn -> {
                    BasicKPIStateInfo txnStateInfo = getTxnBasicInfo(violatedEventProto.getAccountId(), txn);
                    if (txnStateInfo == null) {
                        log.info("Transaction kpi violation is ignored. Reason: Invalid transaction kpi info. Transaction kpi details: [{}]", txn);
                        return null;
                    }
                    if (!txnStateInfo.getGenerateAnomaly()) {
                        log.info("Transaction kpi violation is ignored. Reason: Generate Anomaly is set to false. Transaction kpi details: [{}]", txn);
                        return null;
                    }
                    if (!txnStateInfo.getIsMaintenanceExcluded()) {
                        List<String> validServices = getSvsNotUnderMaintenanceWindow(Collections.singletonList(txn.getTxnInfo().getSvcId()),
                                violatedEventProto.getAccountId(), violatedEventProto.getViolationTmeInGMT());
                        if (validServices.isEmpty()) {
                            log.info("Transaction kpi violation is ignored. Reason: Service is under maintenance. Transaction kpi details: [{}]", txn);
                            return null;
                        }
                    }

                    ViolatedEventProtos.TransactionInfo txnInfo = txn.getTxnInfo();

                    ViolatedData violatedData = getViolatedData(violatedEventProto, violatedTimeInEpochSec);
                    violatedData.setKpiViolationTime(timeInGMT);
                    violatedData.setValue(txn.getValue());
                    violatedData.setEventType(ViolationEventType.TXN_VIOLATION);
                    violatedData.setServiceList(Collections.singletonList(txnInfo.getSvcId()));
                    violatedData.setTransactionId(txnInfo.getTransactionId());
                    violatedData.setResponseTimeType(txnInfo.getResponseTimeType().name());
                    violatedData.setKpiId(txnInfo.getKpiId());
                    violatedData.setOperationType(txnInfo.getOperationType());
                    violatedData.setInstanceId(txnInfo.getTransactionId());
                    violatedData.setThresholds(txnInfo.getThresholdsMap());
                    violatedData.setThresholdSeverity(StringUtils.isEmpty(txnInfo.getThresholdSeverity()) ? lowSeverityIdSignal : txnInfo.getThresholdSeverity());
                    violatedData.setKpiType(txnStateInfo.getKpiType());
                    violatedData.setIsInfo(txnStateInfo.getIsInfo());

                    Map<String, String> metaDataMap = new HashMap<>(txnInfo.getMetaDataMap());
                    if (!StringUtils.isEmpty(txnInfo.getAgentId())) {
                        metaDataMap.put("agentUid", txnInfo.getAgentId());
                    }
                    violatedData.setMetaData(metaDataMap);

                    log.debug("Transaction violatedData [{}]", violatedData);

                    List<ViolatedData> violatedDataList = checkViolationsGetHighestSeverity(violatedData);

                    return violatedDataList != null ? violatedDataList.stream() : Stream.empty();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<ViolatedData> getBatchJobViolatedData(ViolatedEventProtos.ViolatedEvent violatedEventProto, String timeInGMT, long violatedTimeInEpochSec) {
        List<ViolatedEventProtos.BatchJob> batchJobs = violatedEventProto.getBatchJobList();

        return batchJobs.parallelStream().map(batchJob -> {

                    ComponentKpiEntity basicKpiEntity = redisUtilities.getComponentKPIDetails(globalAccountIdentifier, batchComponentIdentifier, batchJob.getKpiId());
                    if(basicKpiEntity == null) {
                        log.error("Batch job violated will be ignored because the kpi doesn't exists. BatchJob:{}, kpiId:{}", batchJob.getBatchJob(), batchJob.getKpiId());
                        return null;
                    }

                    ViolatedData violatedData = getViolatedData(violatedEventProto, violatedTimeInEpochSec);
                    violatedData.setBatchJob(batchJob.getBatchJob());
                    violatedData.setKpiViolationTime(timeInGMT);
                    violatedData.setKpiId(batchJob.getKpiId());
                    violatedData.setEventType(ViolationEventType.BATCH_JOB_VIOLATION);
                    violatedData.setThresholds(batchJob.getThresholdsMap());
                    violatedData.setValue(batchJob.getValue());
                    violatedData.setOperationType(batchJob.getOperationType());
                    violatedData.setKpiAttribute(Constants.COMMON_ATTRIBUTE);
                    violatedData.setThresholdSeverity(StringUtils.isEmpty(batchJob.getThresholdSeverity()) ? lowSeverityIdSignal : batchJob.getThresholdSeverity());

                    Map<String, String> metaDataMap = new HashMap<>(batchJob.getMetaDataMap());
                    if (!StringUtils.isEmpty(batchJob.getAgentId())) {
                        metaDataMap.put("agentUid", batchJob.getAgentId());
                    }
                    violatedData.setMetaData(metaDataMap);
                    violatedData.setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.valueOf(basicKpiEntity.getType()));
                    log.debug("Batch job violatedData [{}]", violatedData);

                    return violatedData;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private boolean instanceValidation(String accId, String instanceId) {
        CompInstClusterDetails compInstClusterDetails = cacheWrapper.getInstanceDetails(accId, instanceId);
        return compInstClusterDetails != null;
    }

    private boolean serviceValidation(String accId, List<String> serviceList) {
        for (String c : serviceList) {
            com.heal.configuration.pojos.Service service = cacheWrapper.getServiceDetails(accId, c);
            if (service == null) {
                log.error("Service details unavailable for service identifier [{}] mapped to account [{}]", c, accId);
                return false;
            }
        }

        return true;
    }

    private BasicKPIStateInfo getKpiBasicInfo(ViolatedEventProtos.ViolatedEvent violatedEventProto, ViolatedEventProtos.Kpi kpi) {

        BasicKPIStateInfo basicKPIStateInfo = new BasicKPIStateInfo();
        String accountIdentifier = violatedEventProto.getAccountId();

        try {
            String instanceIdentifier = kpi.getKpiInfo().getInstanceId();
            int kpiId = Integer.parseInt(kpi.getKpiInfo().getKpiId());

            CompInstKpiEntity instanceKpiDetails = cacheWrapper.getInstanceKPIDetails(accountIdentifier, instanceIdentifier, kpiId);
            if (instanceKpiDetails == null) {
                return null;
            }
            log.trace("Instance Kpi Details found {}", instanceKpiDetails);

            boolean generateAnomaly = getGenerateAnomalyStatus(violatedEventProto, instanceKpiDetails, kpi);
            log.trace("Generate anomaly is set to {}", generateAnomaly);

            basicKPIStateInfo.setIsMaintenanceExcluded(instanceKpiDetails.getIsMaintenanceExcluded() == 1);
            basicKPIStateInfo.setIsInfo(instanceKpiDetails.getIsInfo());
            basicKPIStateInfo.setGenerateAnomaly(generateAnomaly);

            if (instanceKpiDetails.getIsGroup()) {
                if (instanceKpiDetails.getAttributeValues() != null) {
                    basicKPIStateInfo.setAttributeDisplayName(instanceKpiDetails.getAttributeValues().getOrDefault(kpi.getKpiInfo().getKpiAttribute(),
                            kpi.getKpiInfo().getKpiAttribute()));
                }
            }

            if (instanceKpiDetails.getType() == null || instanceKpiDetails.getType().trim().isEmpty()) {
                log.error("KPI type unavailable for KPI [{}] for instance [{}]", instanceKpiDetails.getId(), instanceIdentifier);
                return null;
            } else {
                basicKPIStateInfo.setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.valueOf(instanceKpiDetails.getType()));
            }

        } catch (Exception e) {
            log.error("Error while finding basic kpi state details : ", e);
            return null;
        }

        return basicKPIStateInfo;
    }

    public boolean getGenerateAnomalyStatus(ViolatedEventProtos.ViolatedEvent violatedEventProto,
                                            CompInstKpiEntity instanceKpiDetails,
                                            ViolatedEventProtos.Kpi kpi) {

        if (violatedEventProto.getThresholdType().equalsIgnoreCase("Realtime")) {
            log.trace("Realtime data found. Setting generate anomaly flag to true.");
            return true;
        }

        if (instanceKpiDetails.getKpiViolationConfig() == null || instanceKpiDetails.getKpiViolationConfig().isEmpty()
                || !instanceKpiDetails.getKpiViolationConfig().containsKey(kpi.getKpiInfo().getKpiAttribute()) ||
                instanceKpiDetails.getKpiViolationConfig().get(kpi.getKpiInfo().getKpiAttribute()) == null) {
            log.trace("No instance kpi violation config found. Checking generate anomaly flag at service kpi violation config.");
            for (String svcIdentifier : kpi.getKpiInfo().getSvcIdList()) {
                KpiDetails serviceKPIDetails = cacheWrapper.getServiceKPIDetails(violatedEventProto.getAccountId(), svcIdentifier, Integer.parseInt(kpi.getKpiInfo().getKpiId()));
                if (serviceKPIDetails == null) {
                    log.trace("No Service kpi details found for account {}, service {}, kpi {}", violatedEventProto.getAccountId(), svcIdentifier, kpi.getKpiInfo().getKpiId());
                    continue;
                }
                log.trace("Service kpi details found for account {}, service {}, kpi {} :-  {}",
                        violatedEventProto.getAccountId(), svcIdentifier, kpi.getKpiInfo().getKpiId(), serviceKPIDetails);

                if (serviceKPIDetails.getKpiViolationConfig() != null && !serviceKPIDetails.getKpiViolationConfig().isEmpty()) {

                    KpiViolationConfig kpiViolationConfig = serviceKPIDetails.getKpiViolationConfig().get("ALL")
                            .stream()
                            .findAny().orElse(null);

                    if (kpiViolationConfig != null) {
                        return kpiViolationConfig.getGenerateAnomaly() == 1;
                    }
                }
            }
        } else {
            log.trace("Instance kpi violation config found. Checking for generate anomaly flag.");
            return instanceKpiDetails.getKpiViolationConfig().get(kpi.getKpiInfo().getKpiAttribute()).get(0).getGenerateAnomaly() == 1;
        }

        return false;
    }

    private BasicKPIStateInfo getTxnBasicInfo(String accountIdentifier, ViolatedEventProtos.Transaction txn) {
        BasicKPIStateInfo basicKPIStateInfo = new BasicKPIStateInfo();

        try {
            ComponentKpiEntity kpiDetails = cacheWrapper.getComponentKPIDetails(accountIdentifier, transactionComponentIdentifier, txn.getTxnInfo().getKpiId());
            if (kpiDetails == null) {
                return null;
            }

            basicKPIStateInfo.setIsInfo(kpiDetails.getIsInfo());

            if (kpiDetails.getType() == null || kpiDetails.getType().trim().isEmpty()) {
                log.error("KPI type unavailable for KPI [{}] for transaction [{}]", kpiDetails.getId(), txn.getTxnInfo().getTransactionId());
                return null;
            } else {
                basicKPIStateInfo.setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.valueOf(kpiDetails.getType()));
            }

            List<TxnKPIViolationConfig> txnKPIViolationConfigList = cacheWrapper.getTransactionViolationConfigDetails(accountIdentifier, txn.getTxnInfo().getTransactionId());
            Map<Integer, KpiViolationConfig> kpiViolationConfigMap = txnKPIViolationConfigList.parallelStream()
                    .map(TxnKPIViolationConfig::getKpiViolationConfig)
                    .flatMap(Collection::parallelStream)
                    .filter(c -> c.getKpiId() == Integer.parseInt(txn.getTxnInfo().getKpiId()))
                    .collect(Collectors.toMap(KpiViolationConfig::getKpiId, c -> c));

            basicKPIStateInfo.setGenerateAnomaly(kpiViolationConfigMap.isEmpty() || kpiViolationConfigMap.get(Integer.parseInt(txn.getTxnInfo().getKpiId())).getGenerateAnomaly() == 1);
            basicKPIStateInfo.setIsMaintenanceExcluded(!kpiViolationConfigMap.isEmpty() && kpiViolationConfigMap.get(Integer.parseInt(txn.getTxnInfo().getKpiId())).getExcludeMaintenance() == 1);
        } catch (Exception e) {
            log.error("Error while finding basic transaction state details : ", e);
            return null;
        }

        return basicKPIStateInfo;
    }

    public boolean isInstanceMaintenanceWindowOn(String instanceIdentifier, String accountId, String timeInGMT) {
        List<MaintenanceDetails> maintenanceDetailsList = redisUtilities.getInstanceMaintenanceDetails(accountId, instanceIdentifier);

        if (maintenanceDetailsList.isEmpty()) {
            return false;
        }

        if (utils.isUnderMaintenance(timeInGMT, maintenanceDetailsList, 0)) {
            log.warn("Instance [{}] mapped to accountId [{}] is under maintenance.", instanceIdentifier, accountId);
            return true;
        }

        return false;
    }

    public List<String> getSvsNotUnderMaintenanceWindow(List<String> serviceIds, String accountId, String timeInGMT) {
        return serviceIds.stream().map(srv -> {
            List<MaintenanceDetails> maintenanceDetailsList = redisUtilities.getServiceMaintenanceDetails(accountId, srv);

            if (maintenanceDetailsList.isEmpty()) {
                return srv;
            }

            if (utils.isUnderMaintenance(timeInGMT, maintenanceDetailsList, 0)) {
                log.debug("ServiceId:{} mapped to accountId:{} is under maintenance at timeInGMT:{}", srv, accountId, timeInGMT);
                return null;
            }
            return srv;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private ViolatedData getViolatedData(ViolatedEventProtos.ViolatedEvent violatedEventProto,
                                         long violatedTimeInEpochSec) {

        String accountIdentifier = violatedEventProto.getAccountId();
        List<String> appIds = violatedEventProto.getAppIdList();
        int timeZoneOffset = violatedEventProto.getTimezoneOffsetInSeconds();
        String thresholdType = violatedEventProto.getThresholdType();

        ViolatedData violatedData = new ViolatedData(accountIdentifier, appIds);
        violatedData.setThresholdType(thresholdType);
        violatedData.setViolationFor(thresholdType.equalsIgnoreCase("Static") ? "SOR" : "NOR");
        violatedData.setTimezoneOffsetInSeconds(timeZoneOffset);
        violatedData.setViolationTime(violatedTimeInEpochSec * 1000);

        return violatedData;
    }

}
