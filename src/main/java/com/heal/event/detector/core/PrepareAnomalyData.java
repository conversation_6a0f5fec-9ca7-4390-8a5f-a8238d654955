package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.heal.configuration.pojos.CompInstKpiEntity;
import com.heal.configuration.pojos.ComponentKpiEntity;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.event.detector.pojos.AnomalyAccountPojo;
import com.heal.event.detector.utility.Constants;
import com.heal.event.detector.utility.cache.CacheWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 25-01-2022
 */
@Slf4j
@Component
public class PrepareAnomalyData {

    @Value("${heal.transaction.component.identifier:Transaction}")
    private String transactionComponentIdentifier;
    @Value("${heal.batch.component.identifier:BatchProcess}")
    private String batchComponentIdentifier;
    @Value(("${heal.global.account.identifier:e573f852-5057-11e9-8fd2-b37b61e52317}"))
    private String globalAccountIdentifier;
    @Autowired
    CacheWrapper cacheWrapper;

    public List<AnomalyAccountPojo> getAllAnomalies(List<AnomalyEventProtos.AnomalyEvent> anomalyEventList) {
        try {
            return anomalyEventList.parallelStream()
                    .map(anomalyEvent -> {
                        Map<String, String> signalSeverityMap = cacheWrapper.getMstTypes().parallelStream()
                                .filter(c -> c.getTypeName().equalsIgnoreCase("SignalSeverity"))
                                .collect(Collectors.toMap(c -> c.getSubTypeName().toLowerCase(), c -> String.valueOf(c.getSubTypeId())));

                        if (anomalyEvent.getKpis().getSerializedSize() > 0) {
                            AnomalyEventProtos.KpiInfo kpiInfo = anomalyEvent.getKpis();

                            CompInstKpiEntity compInstKpiEntity;
                            if (kpiInfo.getIsWorkload()) {
                                ComponentKpiEntity basicKpiEntity = cacheWrapper.getComponentKPIDetails(globalAccountIdentifier, transactionComponentIdentifier, anomalyEvent.getKpis().getKpiId());
                                compInstKpiEntity = CompInstKpiEntity.builder()
                                        .categoryDetails(basicKpiEntity.getCategoryDetails())
                                        .identifier(basicKpiEntity.getIdentifier())
                                        .type(basicKpiEntity.getType())
                                        .build();
                            } else {
                                compInstKpiEntity = cacheWrapper.getInstanceKPIDetails(anomalyEvent.getAccountId(), kpiInfo.getInstanceId(), Integer.parseInt(kpiInfo.getKpiId()));
                            }

                            String kpiValue = kpiInfo.getValue();
                            String kpiAttribute = kpiInfo.getKpiAttribute();

                            Map<String, Double> thresholds = new HashMap<>(kpiInfo.getThresholdsMap());
                            thresholds.remove("isInformatic");
                            thresholds.remove("anomalyScore");

                            AnomalyAccountPojo pojo = AnomalyAccountPojo.builder().accountIdentifier(anomalyEvent.getAccountId())
                                    .anomalyDetails(Anomalies.builder()
                                            .anomalyId(anomalyEvent.getAnomalyId())
                                            .anomalyTime(anomalyEvent.getEndTimeGMT())
                                            .categoryId(compInstKpiEntity.getCategoryDetails().getIdentifier())
                                            .identifiedTime(anomalyEvent.getAnomalyTriggerTimeGMT())
                                            .kpiAttribute(kpiAttribute)
                                            .kpiId(Long.parseLong(kpiInfo.getKpiId()))
                                            .metadata(kpiInfo.getMetadataMap())
                                            .operationType(anomalyEvent.getOperationType())
                                            .thresholdType(anomalyEvent.getThresholdType())
                                            .lastThresholdsMeet(thresholds)
                                            .value(kpiValue)
                                            .serviceId(new HashSet<>(kpiInfo.getSvcIdList()))
                                            .kpiIdentifier(compInstKpiEntity.getIdentifier())
                                            .anomalyScore(kpiInfo.getMetadataMap().getOrDefault("anomalyScore", ""))
                                            .build()).build();

                            log.debug("KPI related anomaly pushed into OS: {}", pojo);
                            return pojo;
                        }
                        if (anomalyEvent.getBatchInfo().getSerializedSize() > 0) {
                            AnomalyEventProtos.BatchInfo batchInfo = anomalyEvent.getBatchInfo();

                            ComponentKpiEntity basicKpiEntity = cacheWrapper.getComponentKPIDetails(globalAccountIdentifier, batchComponentIdentifier, anomalyEvent.getKpis().getKpiId());

                            AnomalyAccountPojo pojo = AnomalyAccountPojo.builder().accountIdentifier(anomalyEvent.getAccountId())
                                    .anomalyDetails(Anomalies.builder()
                                            .anomalyId(anomalyEvent.getAnomalyId())
                                            .anomalyTime(anomalyEvent.getEndTimeGMT())
                                            .categoryId(basicKpiEntity.getCategoryDetails().getIdentifier())
                                            .identifiedTime(anomalyEvent.getAnomalyTriggerTimeGMT())
                                            .kpiAttribute(Constants.COMMON_ATTRIBUTE)
                                            .kpiId(Long.parseLong(batchInfo.getKpiId()))
                                            .metadata(batchInfo.getMetadataMap())
                                            .operationType(anomalyEvent.getOperationType())
                                            .thresholdType(anomalyEvent.getThresholdType())
                                            .lastThresholdsMeet(batchInfo.getThresholdsMap())
                                            .value(batchInfo.getValue())
                                            .serviceId(Collections.singleton(anomalyEvent.getAppId(0)))
                                            .anomalyScore(batchInfo.getMetadataMap().getOrDefault("anomalyScore", ""))
                                            .kpiIdentifier(basicKpiEntity.getIdentifier())
                                            .build()).build();

                            log.debug("Batch related anomaly pushed into OS: {}", pojo);

                            return pojo;
                        }
                        return null;
                    }).filter(Objects::nonNull).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Exception while creating anomaly data.", e);
        }
        return new ArrayList<>();
    }

}
