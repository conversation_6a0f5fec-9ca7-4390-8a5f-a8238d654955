package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.heal.configuration.pojos.*;
//import com.heal.event.detector.pojos.AnomalyRequest;
import com.heal.event.detector.pojos.PersistenceSuppressionPojo;
import com.heal.event.detector.pojos.ViolatedData;
import com.heal.event.detector.pojos.enums.CommandMetaKey;
import com.heal.event.detector.pojos.enums.ViolationEventType;
import com.heal.event.detector.repo.RedisUtilities;
import com.heal.event.detector.utility.Constants;
import com.heal.event.detector.utility.StringUtils;
import com.heal.event.detector.utility.Utils;
import com.heal.event.detector.utility.cache.CacheWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PersistenceSuppression {

    @Autowired
    RedisUtilities redisUtilities;
    @Autowired
    CacheWrapper cacheWrapper;

    @Value("${signal.severity.id.high:433}")
    String highSeverityIdSignal;

    @Value("${signal.severity.id.medium:432}")
    String mediumSeverityIdSignal;

    @Value("${signal.severity.id.low:431}")
    String lowSeverityIdSignal;

    @Value("${entity.type.instance:INSTANCE}")
    String entityTypeInstance;

    @Value("${entity.type.transaction:TRANSACTION}")
    String entityTypeTransaction;

    @Value("${entity.type.service:SERVICE}")
    String entityTypeService;

    @Value("${heal.transaction.component.identifier:Transaction}")
    private String transactionComponentIdentifier;

    private static final String AE_PREFIX = "AE";
    private static final String AE_SPLITTER = "-";

    public AnomalyEventProtos.AnomalyEvent checkAndApplyPersistenceSuppression(ViolatedData value) {
        log.debug("ViolatedData in PersistenceSuppression before the persistence suppression values are picked up: {}", value);
        try {
            StringBuilder keyBuilder = new StringBuilder();
            String anomalyLevel = "";
            int collectionInterval = 60;
            boolean isTxn = true;

            int persistence = 0;
            int suppression = 0;

            int isInformatic = 0;
            String thresholdType = value.getThresholdType();
            String key = null;
            if (value.getEventType().equals(ViolationEventType.KPI_VIOLATION)) {
                int kpiId = Integer.parseInt(value.getKpiId());
                String accountIdentifier = value.getAccountId();
                String instanceIdentifier = value.getInstanceId();

                CompInstKpiEntity kpiDetails = cacheWrapper.getInstanceKPIDetails(accountIdentifier, instanceIdentifier, kpiId);
                if (kpiDetails == null) {
                    return null;
                }

                isInformatic = kpiDetails.getIsInfo();
                collectionInterval = kpiDetails.getCollectionInterval();///60
                Map<String, List<KpiViolationConfig>> attributeViolationConfig = kpiDetails.getKpiViolationConfig();
                List<KpiViolationConfig> kpiViolationConfigList = attributeViolationConfig.get(value.getKpiAttribute());

                if (kpiViolationConfigList == null || kpiViolationConfigList.isEmpty()) {
                    kpiViolationConfigList = attributeViolationConfig.get("ALL");
                }

                for (KpiViolationConfig vd : kpiViolationConfigList) {
                    if (vd != null && value.getThresholdSeverity().equals(String.valueOf(vd.getThresholdSeverityId()))) {
                        persistence = vd.getPersistence();
                        suppression = vd.getSuppression();
                    }
                }
                
                isTxn = false;
                anomalyLevel = entityTypeInstance;

                key = Utils.stringBuilderHelper(keyBuilder, accountIdentifier, value.getAppIds().get(0),
                        instanceIdentifier, value.getKpiId(), value.getKpiAttribute(), value.getViolationFor());
            } else if (value.getEventType().equals(ViolationEventType.TXN_VIOLATION)) {
                int kpiId = Integer.parseInt(value.getKpiId());
                String accountIdentifier = value.getAccountId();
                String txnIdentifier = value.getTransactionId();

                ComponentKpiEntity componentKPIDetails = cacheWrapper.getComponentKPIDetails(accountIdentifier, transactionComponentIdentifier, String.valueOf(kpiId));
                if (componentKPIDetails == null) {
                    return null;
                }

                List<TxnKPIViolationConfig> txnKPIViolationConfigList = cacheWrapper.getTransactionViolationConfigDetails(accountIdentifier, txnIdentifier);
                Map<String, KpiViolationConfig> kpiViolationConfigMap = txnKPIViolationConfigList.parallelStream()
                        .map(TxnKPIViolationConfig::getKpiViolationConfig)
                        .flatMap(Collection::parallelStream)
                        .filter(c -> c.getKpiId() == kpiId)
                        .collect(Collectors.toMap(KpiViolationConfig::getDefinedBy, c -> c));

                if (kpiViolationConfigMap.containsKey("USER")) {
                    persistence = kpiViolationConfigMap.get("USER").getPersistence();
                    suppression = kpiViolationConfigMap.get("USER").getSuppression();
                } else if (kpiViolationConfigMap.containsKey("SYSTEM")) {
                    persistence = kpiViolationConfigMap.get("SYSTEM").getPersistence();
                    suppression = kpiViolationConfigMap.get("SYSTEM").getSuppression();
                }

                isInformatic = componentKPIDetails.getIsInfo();
                collectionInterval = componentKPIDetails.getCommonVersionDetails().get(0).getCollectionInterval();
                anomalyLevel = entityTypeTransaction;

                key = Utils.stringBuilderHelper(keyBuilder, value.getAccountId(), value.getAppIds().get(0),
                        String.valueOf(txnIdentifier.hashCode()), value.getKpiId(), value.getKpiAttribute() == null ? "ALL" : value.getKpiAttribute(), value.getViolationFor());
            }

            if (key == null) {
                log.error("EventDetector Persistence Suppression Key formed is null");
                return null;
            }

            String serviceId = value.getMetaData().getOrDefault("serviceIdentifier", value.getServiceList().get(0));

            if (persistence <= 0 || suppression <= 0) {
                log.warn("Persistence suppression conf unavailable at either instance {} or transaction {} level. Looking for the same conf at service {} level.",
                        value.getInstanceId(), value.getTransactionId(), serviceId);

                ServiceConfiguration persistenceSuppressionConf = getPersistenceSuppressionServiceConf(value.getAccountId(), serviceId, collectionInterval);

                if (persistenceSuppressionConf == null) {
                    log.warn("Persistence suppression conf unavailable for account [{}], application [{}], instance [{}], " +
                                    "kpiId [{}], collection interval [{}], and services [{}]", value.getAccountId(),
                            value.getAppIds(), value.getInstanceId(), value.getKpiId(), collectionInterval, serviceId);
                    return null;
                }

                int startCollectionInterval = persistenceSuppressionConf.getAnomalyConfiguration().getStartCollectionInterval();
                int endCollectionInterval = persistenceSuppressionConf.getAnomalyConfiguration().getEndCollectionInterval();

                // Get persistence and suppression based on threshold severity
                if (value.getThresholdSeverity().equals(lowSeverityIdSignal)) {
                    persistence = persistenceSuppressionConf.getAnomalyConfiguration().getLowPersistence();
                    suppression = persistenceSuppressionConf.getAnomalyConfiguration().getLowSuppression();
                } else if (value.getThresholdSeverity().equals(mediumSeverityIdSignal)) {
                    persistence = persistenceSuppressionConf.getAnomalyConfiguration().getMediumPersistence();
                    suppression = persistenceSuppressionConf.getAnomalyConfiguration().getMediumSuppression();
                } else if (value.getThresholdSeverity().equals(highSeverityIdSignal)) {
                    persistence = persistenceSuppressionConf.getAnomalyConfiguration().getHighPersistence();
                    suppression = persistenceSuppressionConf.getAnomalyConfiguration().getHighSuppression();
                }

                if (persistence <= 0 || suppression <= 0) {
                    return null;
                }

                value.setServiceList(Collections.singletonList(serviceId));
                value.getMetaData().put("serviceIdentifiers", serviceId);

                log.info("Persistence suppression conf is available at service level. Details: Persistence [{}], suppression [{}], startCollectionInterval [{}], " +
                                "endCollectionInterval [{}], collectionInterval [{}], thresholdType [{}] for kpiID [{}] of serviceID [{}]",
                        persistence, suppression, startCollectionInterval, endCollectionInterval,
                        collectionInterval, thresholdType, value.getKpiId(), serviceId);
                anomalyLevel = entityTypeService;
            } else {
                log.info("Persistence suppression conf is available at instance level. Details: Persistence [{}], suppression [{}] for kpiID [{}] mapped to instance [{}]",
                        persistence, suppression, value.getKpiId(), value.getInstanceId());
            }

            long currentTimeCollectionTime = value.getViolationTime();
            PersistenceSuppressionPojo persistenceSuppressionPojo = redisUtilities.getPersistenceSuppressionDetails(key);
            long previousCollectionTime = 0L;
            if (persistenceSuppressionPojo != null) {
                if (persistenceSuppressionPojo.getPersistence() == 0 && persistenceSuppressionPojo.getSuppression() == 0) {
                    previousCollectionTime = persistenceSuppressionPojo.getLastViolationTime();

                    persistenceSuppressionPojo.setPersistence(persistence);
                    persistenceSuppressionPojo.setSuppression(suppression);
                    redisUtilities.putPersistenceSuppressionDetails(key, persistenceSuppressionPojo);
                } else if (persistenceSuppressionPojo.getPersistence() != persistence || persistenceSuppressionPojo.getSuppression() != suppression) {
                    redisUtilities.deletePersistenceSuppressionDetails(key);
                } else {
                    previousCollectionTime = persistenceSuppressionPojo.getLastViolationTime();
                }
            }

            //Initialize the buckets in 2 conditions:
            //1. If previous collection time is 0 that indicates first occurrence of violated event.
            //2. If the time difference between previousCollectionTime and currentCollectionTime is greater than collection interval,
            // then the current violated event should be considered as first occurrence.

            //In VEProtoToVD, violationTime is changed from seconds to milliseconds to ensure that right data gets displayed in raw_kpis_violation table
            long timeDiff = (currentTimeCollectionTime - previousCollectionTime) / 1000;

            if (timeDiff <= 0) {
                log.error("Invalid time difference found between last message violation time {} and current message violation time {} for account {}," +
                                "application {}, instance {}, kpi {}, kpi attribute {}, violation type {}. Ignoring the duplicate violation message.",
                        previousCollectionTime, currentTimeCollectionTime, value.getAccountId(), value.getAppIds().get(0), value.getInstanceId(),
                        value.getKpiId(), value.getKpiAttribute(), value.getViolationFor());
                return null;
            }

            if (previousCollectionTime == 0 || timeDiff > collectionInterval) {
                persistenceSuppressionPojo = PersistenceSuppressionPojo.builder()
                        .violationStartTime(currentTimeCollectionTime)
                        .lastViolationTime(currentTimeCollectionTime)
                        .violationCount(0)
                        .persistence(persistence)
                        .suppression(suppression)
                        .build();
                redisUtilities.putPersistenceSuppressionDetails(key, persistenceSuppressionPojo);
            }

            // New Flow
            applyPersistenceSuppressionCreateUpdateAnomaly(value, isTxn, isInformatic);

            // Old Flow Can be removed
            return applyPersistenceSuppressionBasedOnType(value, key, currentTimeCollectionTime, persistence, suppression, isTxn, isInformatic, anomalyLevel, collectionInterval);
        } catch (Exception e) {
            log.error("Exception while processing persistence and suppression values for violated data [{}]. Details: ", value, e);
        }
        return null;
    }

    /**
     * Returns persistence and suppression values for the given context (account,
     * instance/txn, kpi, service, severity).
     * Looks up the correct ServiceConfiguration and severity
     *
     * @param entityType              INSTANCE or TRANSACTION
     * @param accountIdentifier       account id
     * @param instanceIdentifier      instance id (or txn id for TRANSACTION)
     * @param kpiId                   KPI id
     * @param serviceId               service id
     * @param severityId              severity id (string)
     * @return Map with keys "persistence", "suppression" and "collectionInterval"
     *         or null if not found
     */
    public Map<String, Integer> getPersSuppAndCollectionIntervalAtServiceConf(String entityType,
            String accountIdentifier, String instanceIdentifier, String kpiId, String serviceId,
            String severityId) {
        int persistence = 0;
        int suppression = 0;
        int collectionInterval = 60;
        Map<String, Integer> persSupp = new HashMap<>();

        if (entityType.equals(entityTypeInstance)) {
            CompInstKpiEntity kpiDetails = cacheWrapper.getInstanceKPIDetails(accountIdentifier, instanceIdentifier,
                    Integer.parseInt(kpiId));
            if (kpiDetails == null) {
                return null;
            }
            collectionInterval = kpiDetails.getCollectionInterval();
        }
        if (entityType.equals(entityTypeTransaction)) {
            ComponentKpiEntity componentKPIDetails = cacheWrapper.getComponentKPIDetails(accountIdentifier,
                    transactionComponentIdentifier, kpiId);
            if (componentKPIDetails == null) {
                return null;
            }
            collectionInterval = componentKPIDetails.getCommonVersionDetails().get(0).getCollectionInterval();
        }
        persSupp.put("collectionInterval", collectionInterval);

        ServiceConfiguration serviceConfiguration = null;
        try {
            serviceConfiguration = getPersistenceSuppressionServiceConf(accountIdentifier, serviceId,
                    collectionInterval);
        } catch (Exception e) {
            log.error("Error fetching service configuration for account [{}], instance [{}], kpiId [{}], " +
                    "collection interval [{}], and services [{}]. Details: ", accountIdentifier, instanceIdentifier,
                    kpiId, collectionInterval, serviceId, e);
            return persSupp;
        }

        if (serviceConfiguration == null) {
            log.warn("Persistence suppression conf unavailable for account [{}], instance [{}], " +
                    "kpiId [{}], collection interval [{}], and services [{}]", accountIdentifier,
                    instanceIdentifier, kpiId, collectionInterval, serviceId);
            return persSupp;
        }
        if (severityId != null) {
            if (severityId.equals(lowSeverityIdSignal)) {
                persistence = serviceConfiguration.getAnomalyConfiguration().getLowPersistence();
                suppression = serviceConfiguration.getAnomalyConfiguration().getLowSuppression();
            } else if (severityId.equals(mediumSeverityIdSignal)) {
                persistence = serviceConfiguration.getAnomalyConfiguration().getMediumPersistence();
                suppression = serviceConfiguration.getAnomalyConfiguration().getMediumSuppression();
            } else if (severityId.equals(highSeverityIdSignal)) {
                persistence = serviceConfiguration.getAnomalyConfiguration().getHighPersistence();
                suppression = serviceConfiguration.getAnomalyConfiguration().getHighSuppression();
            }
        }
        persSupp.put("persistence", persistence);
        persSupp.put("suppression", suppression);
        return persSupp;
    }

    /**
     * This method checks if the persistence suppression condition is met for a given
     * severity ID in the ViolationDetails.
     *
     * @param violationDetails The ViolationDetails object containing the violation status map.
     * @param severityId       The severity ID to check against.
     * @return true if the persistence suppression condition is met, false otherwise.
     */
    public Boolean persistenceSuppressionMetForSeverity(ViolationDetails violationDetails, String severityId) {
        if (violationDetails == null || violationDetails.getViolationStatusMap() == null) {
            log.error("ViolationDetails or ViolationStatusMap is null for severityId: {}", severityId);
            return false;
        }
        if (violationDetails.getViolationStatusMap().containsKey(severityId) == false) {
            log.error("SeverityId {} not found in ViolationStatusMap", severityId);
            return false;
        }

        ViolationStatus violationStatus = violationDetails.getViolationStatusMap().get(severityId);
        if (violationStatus == null) {
            log.error("No ViolationStatus found for severityId: {}", severityId);
            return false;
        }

        int violationCount = violationStatus.getViolationCount();
        int persistence = violationStatus.getPersistence();
        int suppression = violationStatus.getSuppression();

        if (violationCount < persistence) {
            return false;
        }
        if (violationCount == persistence) {
            return true;
        }
        return (violationCount - persistence) % suppression == suppression - 1;
    }

    /**
     * This method applies persistence suppression logic to create or update anomalies based on the violated data.
     * It checks the violation count against the configured persistence and suppression values.
     *
     * @param violatedData The data that has violated the KPI thresholds.
     * @param isTxn        Indicates if the violation is for a transaction.
     */
    public void applyPersistenceSuppressionCreateUpdateAnomaly(ViolatedData violatedData, boolean isTxn, int isInformatic) {
        // Defensive null checks
        ViolationDetails violationDetails = redisUtilities.getViolationDetails(
                violatedData.getAccountId(),
                violatedData.getInstanceId(),
                violatedData.getKpiId(),
                violatedData.getKpiAttribute(),
                violatedData.getViolationFor(),
                isTxn ? violatedData.getTransactionId() : null);
        if (violationDetails == null) {
            log.error("No ViolationDetails found for violatedData: {}", violatedData);
            return;
        }
        ViolationStatus violationStatusHighestSeverity = violationDetails.getViolationStatusMap()
                .get(violationDetails.getHighestViolatedSeverity());
        if (violationStatusHighestSeverity == null) {
            log.error("No ViolationStatus found for highest severity [{}] in violatedData: {}", violationDetails.getHighestViolatedSeverity(), violatedData);
            return;
        }

        // Defensive copy of metaData to avoid side effects
        Map<String, String> metaData = new HashMap<>(violatedData.getMetaData());
        metaData.put("isMaintenanceExcluded", violatedData.isMaintenanceExcluded() ? "1" : "0");
        metaData.put("violationLevel", violationDetails.getViolationLevel());
        if (!metaData.containsKey("serviceIdentifier")) {
            metaData.put("serviceIdentifier", String.join(", ", violatedData.getServiceList()));
        }
        metaData.put("isInformatic", String.valueOf(isInformatic));
        metaData.putIfAbsent("kpiType", violatedData.getKpiType().name());
        metaData.put("attributeName", violatedData.getKpiAttribute());
        metaData.put("closingWindow", String.valueOf(violationDetails.getClosingWindowCount()));
        metaData.put("maxDataBreaks", String.valueOf(violationDetails.getDataBreakCount()));

//        AnomalyRequest anomalyRequest = AnomalyRequest.builder()
//                .accountIdentifier(violatedData.getAccountId())
//                .entityId(isTxn ? violatedData.getTransactionId() : violatedData.getInstanceId())
//                .entityType(isTxn ? entityTypeTransaction : entityTypeInstance)
//                .kpiId(Long.parseLong(violatedData.getKpiId()))
//                .kpiAttribute(violatedData.getKpiAttribute())
////                 TODO: Is categoryId value being set correct?
//                .categoryId(violatedData.getKpiType().name())
//                .serviceId(new HashSet<>(violatedData.getServiceList()))
//                .thresholdType(violatedData.getThresholdType())
//                .operationType(violatedData.getOperationType())
//                .value(violatedData.getValue())
//                .lastThresholdsMeet(violationStatusHighestSeverity.getThresholds())
//                .metadata(metaData)
////                 TODO: Is identifiedTime value being set correct?
//                .identifiedTime(violatedData.getViolationTime())
//                .lastSeverityId(Integer.parseInt(violationDetails.getHighestViolatedSeverity()))
//                .violationFor(violatedData.getViolationFor())
//                .anomalyScore(metaData.get("anomalyScore"))
//                .build();

        int violationCount = violationStatusHighestSeverity.getViolationCount();
        int persistence = violationStatusHighestSeverity.getPersistence();
        int suppression = violationStatusHighestSeverity.getSuppression();

        if (violationCount < persistence) {
            log.info("Violation count [{}] is less than persistence [{}] for kpiId [{}] mapped to instance [{}] and service [{}]. No anomaly created.",
                    violationCount, persistence, violatedData.getKpiId(), violatedData.getInstanceId(), violatedData.getServiceList());
            return;
        } else if (violationCount == persistence) {
            log.info("Violation count [{}] is equal to persistence [{}] for kpiId [{}] mapped to instance [{}] and service [{}]. Anomaly created.",
                    violationCount, persistence, violatedData.getKpiId(), violatedData.getInstanceId(), violatedData.getServiceList());
//            anomalyRequest.setStartSeverityId(Integer.parseInt(violationDetails.getHighestViolatedSeverity()));
//            anomalyManagementService.createAnomaly(anomalyRequest, isTxn);
        } else if ((violationCount - persistence) % suppression == suppression - 1) {
            log.info("Violation count [{}] is greater than persistence [{}] + suppression [{}] for kpiId [{}] mapped to instance [{}] and service [{}]. Anomaly updated.",
                    violationCount, persistence, suppression, violatedData.getKpiId(), violatedData.getInstanceId(), violatedData.getServiceList());
//            anomalyManagementService.updateAnomaly(anomalyRequest);
        } else {
            log.info("Violation count [{}] is suppressed for persistence [{}] and suppression [{}] for kpiId [{}] mapped to instance [{}] and service [{}]. No anomaly created.",
                    violationCount, persistence, suppression, violatedData.getKpiId(), violatedData.getInstanceId(), violatedData.getServiceList());
        }
    }

    public ServiceConfiguration getPersistenceSuppressionServiceConf(String accountId, String srv, int collectionInterval) {
        com.heal.configuration.pojos.Service service = cacheWrapper.getServiceDetails(accountId, srv);
        if (service == null) {
            return null;
        }

        List<ServiceConfiguration> persistenceSuppressionConfList = service.getServiceConfigurations();

        if (Objects.isNull(persistenceSuppressionConfList) || persistenceSuppressionConfList.isEmpty()) {
            return null;
        }

        long collectionIntervalInMinute = collectionInterval / 60;

        return persistenceSuppressionConfList.stream()
                .filter(conf -> conf.getAnomalyConfiguration().getStartCollectionInterval() <= collectionIntervalInMinute &&
                        collectionIntervalInMinute <= conf.getAnomalyConfiguration().getEndCollectionInterval())
                .findFirst()
                .orElse(null);

    }

    // Old Flow Can be removed
    private AnomalyEventProtos.AnomalyEvent applyPersistenceSuppressionBasedOnType(ViolatedData violatedData, String key,
                                                                                   long currentTimeCollectionTime, int persistenceVal,
                                                                                   int suppressionVal, boolean isTxn,
                                                                                   int isInformatic, String anomalyLevel,
                                                                                   int collectionInterval) {

        /*
         * If again violation is found for next aggregated data then update the count and last collection time
         */
        PersistenceSuppressionPojo persistenceSuppressionPojo = redisUtilities.syncAndGetPersistenceSuppressionDetails(key, currentTimeCollectionTime);

        /*
         * If total count is equals to persistence count then collect this data as issue object
         */

        log.debug("Total violation count is [{}] since previous anomaly creation for kpiId mapped to instance [{}] and service [{}]",
                persistenceSuppressionPojo.getViolationCount(), violatedData.getInstanceId(), violatedData.getServiceList());

        if (persistenceSuppressionPojo.getViolationCount() == persistenceVal) {
            long anomalyTime = TimeUnit.MINUTES.toMillis(TimeUnit.MILLISECONDS.toMinutes(System.currentTimeMillis()));
            String anomalyEvent = getAnomalyIdentifier(violatedData, isTxn);
            violatedData.setPersistence(persistenceVal);
            violatedData.setSuppression(suppressionVal);

            AnomalyEventProtos.AnomalyEvent.Builder anomalyEventBuilder = AnomalyEventProtos.AnomalyEvent.newBuilder();
            anomalyEventBuilder.setAccountId(violatedData.getAccountId());
            anomalyEventBuilder.addAllAppId(new ArrayList<>(violatedData.getAppIds()));
            anomalyEventBuilder.setAnomalyId(anomalyEvent);
            anomalyEventBuilder.setThresholdType(violatedData.getThresholdType());
            anomalyEventBuilder.setOperationType(violatedData.getOperationType());
            //violation time in ViolatedData is in milliseconds.
            anomalyEventBuilder.setStartTimeGMT(persistenceSuppressionPojo.getViolationStartTime());
            anomalyEventBuilder.setEndTimeGMT(violatedData.getViolationTime());
            anomalyEventBuilder.setAnomalyTriggerTimeGMT(anomalyTime);
            AnomalyEventProtos.KpiInfo.Builder kpiInfo = AnomalyEventProtos.KpiInfo.newBuilder();
            kpiInfo.setKpiId(violatedData.getKpiId());
            kpiInfo.addAllSvcId(violatedData.getServiceList());
            Map<String, Double> thresholds = new HashMap<>(violatedData.getThresholds());
            thresholds.put("isInformatic", (double) isInformatic);
            kpiInfo.putAllThresholds(thresholds);
            kpiInfo.setValue(violatedData.getValue());

            if (null != violatedData.getThresholdSeverity()) {
                kpiInfo.setThresholdSeverity(violatedData.getThresholdSeverity());
            }

            long expectedAnomalyClosingTime = TimeUnit.SECONDS.toMillis(TimeUnit.MILLISECONDS.toSeconds(anomalyTime)
                    + ((long) (suppressionVal + persistenceVal - 1) * collectionInterval));

            kpiInfo.putMetadata(CommandMetaKey.KPI_VIOLATION_TIME.getKey(), violatedData.getKpiViolationTime());
            kpiInfo.putAllMetadata(violatedData.fetchMetaData());
            Map<String, String> metaData = new HashMap<>();
            metaData.put("persistence", String.valueOf(violatedData.getPersistence()));
            metaData.put("suppression", String.valueOf(violatedData.getSuppression()));
            metaData.put("starttime", String.valueOf(violatedData.getViolationTime()));
            if (!kpiInfo.getMetadataMap().containsKey("serviceIdentifiers")) {
                metaData.put("serviceIdentifiers", String.join(", ", violatedData.getServiceList()));
            }
            metaData.put("thresholdseverity", kpiInfo.getThresholdSeverity());
            metaData.put("isMaintenanceExcluded", violatedData.isMaintenanceExcluded() ? "1" : "0");
            metaData.putIfAbsent("kpiType", violatedData.getKpiType().name());
            metaData.put("anomalyLevel", anomalyLevel);
            metaData.put("expectedAnomalyClosingTime", String.valueOf(expectedAnomalyClosingTime));
            metaData.put("Collection_Interval", String.valueOf(collectionInterval));
            kpiInfo.putAllMetadata(metaData);

            if (violatedData.getEventType().toString().equals(ViolationEventType.KPI_VIOLATION.toString())) {
                kpiInfo.setInstanceId(violatedData.getInstanceId());
                kpiInfo.setIsWorkload(false);
                kpiInfo.setKpiAttribute(violatedData.getKpiAttribute());
            } else if (violatedData.getEventType().toString().equals(ViolationEventType.TXN_VIOLATION.toString())) {
                kpiInfo.setInstanceId(violatedData.getTransactionId());
                kpiInfo.setIsWorkload(true);
                kpiInfo.setKpiAttribute(violatedData.getResponseTimeType());
            }

            anomalyEventBuilder.setKpis(kpiInfo.build());
            AnomalyEventProtos.AnomalyEvent event = anomalyEventBuilder.build();

            log.info("Anomaly created: {}", event);

            return event;
        } else {
            log.info("Anomaly will not be created as number of violated events persisted [{}] does not fall under " +
                    "the configured persistence value [{}] and suppression value [{}].", persistenceSuppressionPojo.getViolationCount(), persistenceVal, suppressionVal);
        }

        /*
         * Reset the data when suppression condition is satisfied
         */
        if (persistenceSuppressionPojo.getViolationCount() == suppressionVal + persistenceVal) {
            redisUtilities.deletePersistenceSuppressionDetails(key);

            log.info("Violated events will not be suppressed for the next persisted violation event for kpiID [{}] mapped to instance [{}] and service [{}]",
                    violatedData.getKpiId(), violatedData.getInstanceId(), violatedData.getServiceList());
        }

        return null;
    }

    public String getAnomalyIdentifier(ViolatedData value, boolean isTxn) {
        String accountId = value.getAccountId();
        String instId = value.getInstanceId();

        Account accountData = cacheWrapper.getAccountDetails(accountId);

        String anomalyEvent = null;

        if (isTxn) {
            Transaction txnData = cacheWrapper.getTransactionDetails(accountId, value.getTransactionId());
            if (Objects.nonNull(accountData) && Objects.nonNull(txnData)) {
                anomalyEvent = AE_PREFIX + AE_SPLITTER + accountData.getId() +
                        AE_SPLITTER +
                        txnData.getId() +
                        AE_SPLITTER +
                        value.getKpiId() +
                        AE_SPLITTER +
                        "T" +
                        AE_SPLITTER +
                        (value.getViolationFor().equals("SOR") ? "S" : "N") +
                        AE_SPLITTER +
                        TimeUnit.MILLISECONDS.toMinutes(value.getViolationTime());
            }
        } else {
            boolean isGroupKpi = value.getMetaData() != null && Boolean.parseBoolean(value.getMetaData().getOrDefault("isGroupKpi", (StringUtils.isEmpty(value.getKpiAttribute())
                    && !value.getKpiAttribute().trim().equals(Constants.COMMON_ATTRIBUTE)) ? "true" : "false"));

            CompInstClusterDetails instData = cacheWrapper.getInstanceDetails(accountId, instId);
            if (Objects.nonNull(accountData) && Objects.nonNull(instData)) {
                anomalyEvent = AE_PREFIX + AE_SPLITTER + accountData.getId() +
                        AE_SPLITTER +
                        instData.getId() +
                        AE_SPLITTER +
                        value.getKpiId() +
                        AE_SPLITTER +
                        "C" +
                        AE_SPLITTER +
                        (value.getViolationFor().equals("SOR") ? "S" : "N") +
                        AE_SPLITTER +
                        (!StringUtils.isEmpty(value.getKpiAttribute()) ? (isGroupKpi ? String.valueOf(value.getKpiAttribute().trim().hashCode()) : value.getKpiAttribute().trim()).concat(AE_SPLITTER) : "") +
                        TimeUnit.MILLISECONDS.toMinutes(value.getViolationTime());
            }
        }

        if (anomalyEvent == null) {
            String anomalyIdPrefix = value.getViolationFor().equals("SOR") ? "AE-S-" : "AE-N-";
            anomalyEvent = anomalyIdPrefix + UUID.randomUUID();
        }

        return anomalyEvent;
    }
}
