package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.AggregatedKpiProtos;
import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.appnomic.appsone.common.protbuf.ThresholdProtos;
import com.appnomic.appsone.common.protbuf.ViolatedEventProtos;
import com.heal.configuration.pojos.ApplicationSettings;
import com.heal.configuration.pojos.ViewTypes;
import com.heal.configuration.protbuf.ThresholdsClosedProtos;
import com.heal.event.detector.pojos.GenericValidationObject;
import com.heal.event.detector.pojos.ViolatedData;
import com.heal.event.detector.service.EventForwarderToQueue;
import com.heal.event.detector.utility.HealthMetrics;
import com.heal.event.detector.utility.cache.CacheWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProtoValidation {
    @Value("${forward.raw.anomalies:false}")
    boolean pushRawAnomalies;
    @Autowired
    ViolatedEventsProcess violatedEventsProcess;
    @Autowired
    AnomalyEventsProcess anomalyEventsProcess;
    @Autowired
    ThresholdDataProcess thresholdDataProcess;
    @Autowired
    ClosedNorThresholdDataProcess closedNorThresholdDataProcess;
    @Autowired
    EventForwarderToQueue forwarder;
    @Autowired
    GetViolatedData getViolatedData;
    @Autowired
    HealthMetrics metrics;
    @Autowired
    CacheWrapper cacheWrapper;

    public void validateAndProcessInputAggregatedKPI(AggregatedKpiProtos.AggregatedKpi aggregatedKpi) {

        long aggKpiProcessStartTime = System.currentTimeMillis();
        try {
            GenericValidationObject<AggregatedKpiProtos.AggregatedKpi> validateObject = violatedEventsProcess.validateAggregatedKpiData(aggregatedKpi);
            if (!validateObject.isValid()) {
                return;
            }
            aggregatedKpi = validateObject.getProto();

            List<ViolatedData> violatedDataList = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
            if (violatedDataList.isEmpty()) {
                return;
            }

            log.info("Processed AggregatedKpi proto into ViolatedData successfully. Sending it for further processing. " +
                    "Violations size : {}", violatedDataList.size());
            metrics.updateViolationCount(violatedDataList.size());

            List<AnomalyEventProtos.AnomalyEvent> anomalyEventList = anomalyEventsProcess.processViolatedKpiData(violatedDataList);
            if (anomalyEventList.isEmpty()) {
                return;
            }
            log.info("Processed Violated data into Anomaly Event proto successfully. " +
                    "Sending it to queue for further processing. Anomalies size:{}", anomalyEventList.size());

            metrics.updateAnomalyCount(anomalyEventList.size());
            forwarder.sendAnomalyOutputToActionQueue(anomalyEventList);
            List<ViewTypes> mstSubType = cacheWrapper.getMstTypes();
            Map<String, Integer> subTypeMap = mstSubType.parallelStream().filter(c -> c.getTypeName().equalsIgnoreCase(
                    "AnomalySignalType")).collect(Collectors.toMap(ViewTypes::getSubTypeName, ViewTypes::getSubTypeId));
            log.debug("total anomaly event created is {}", anomalyEventList.size());

            anomalyEventList.forEach(c -> {
                if(!c.getAppIdList().isEmpty()) {
                    ApplicationSettings applicationSettings = cacheWrapper.getApplicationSettings(c.getAccountId(),
                            c.getAppId(0));
                    log.debug("processing anomaly for appid {}, accountId {}", c.getAppId(0), c.getAccountId());

                    if (Objects.isNull(applicationSettings)) {
                        forwarder.sendAnomalyOutputToSignalQueue(c);
                        if (pushRawAnomalies) {
                            log.debug("The consul key 'anomaly.itsm.forward' is set to true. Anomaly details will be forwarded to Notification-Processor. Details: Anomaly Id {}, AppId {}, Account Id {}, Trigger Time(in GMT) {}", c.getAnomalyId(), c.getAppId(0), c.getAccountId(), c.getAnomalyTriggerTimeGMT());
                            forwarder.sendAnomalyMessagesQueue(c);
                        }
                    } else {
                        int subTypeId = subTypeMap.getOrDefault(applicationSettings.getTypeName(), 413);
                        if (subTypeId == 413 || applicationSettings.getTypeName().equalsIgnoreCase("signal-detector")) {
                            forwarder.sendAnomalyOutputToSignalQueue(c);
                            if (pushRawAnomalies) {
                                log.info("The consul key 'anomaly.itsm.forward' is set to true. Anomaly details will be forwarded to Notification-Processor. Details: Anomaly Id {}, AppId {}, Account Id {}, Trigger Time(in GMT) {}", c.getAnomalyId(), c.getAppId(0), c.getAccountId(), c.getAnomalyTriggerTimeGMT());
                                forwarder.sendAnomalyMessagesQueue(c);
                            }
                        }

                        if (subTypeId == 413 || applicationSettings.getTypeName().equalsIgnoreCase("event-correlation")) {
                            forwarder.sendAnomalyOutputToMLESignalQueue(c);
                        }
                    }
                }
            });
        } catch (Exception e) {
            log.error("Exception while processing violated kpi data into anomaly kpi data for" +
                    " kpi details {}.", aggregatedKpi, e);
            metrics.updateAggregatedKpiProcessingErrors();
        } finally {
            metrics.updateSlowAggregatedKpiEvents(System.currentTimeMillis() - aggKpiProcessStartTime);
        }
    }

    public void validateAndProcessInputViolatedEvents(ViolatedEventProtos.ViolatedEvent violatedEvent) {

        long violatedEventProcessStartTime = System.currentTimeMillis();
        try {
            GenericValidationObject<ViolatedEventProtos.ViolatedEvent> validateObject = anomalyEventsProcess.validateAnomalyEvent(violatedEvent);
            if (!validateObject.isValid()) {
                return;
            }
            violatedEvent = validateObject.getProto();

            List<ViolatedData> violatedDataList = getViolatedData.getViolatedDataObject(violatedEvent);
            if (violatedDataList.isEmpty()) {
                log.warn("No valid violated data found to process further for account [{}], kpi list {}, txn list {}", violatedEvent.getAccountId(), violatedEvent.getKpisList(), violatedEvent.getTransactionsList());
                return;
            }

            List<AnomalyEventProtos.AnomalyEvent> anomalyEventList = anomalyEventsProcess.processViolatedKpiData(violatedDataList);
            if (anomalyEventList.isEmpty()) {
                return;
            }

            log.info("Processed Violated Event proto into Anomaly Event proto successfully.Sending it to queue for further processing.");

            metrics.updateAnomalyCount(anomalyEventList.size());
            forwarder.sendAnomalyOutputToActionQueue(anomalyEventList);
            List<ViewTypes> mstSubType = cacheWrapper.getMstTypes();
            Map<String, Integer> subTypeMap = mstSubType.parallelStream().filter(c -> c.getTypeName().equalsIgnoreCase(
                    "AnomalySignalType")).collect(Collectors.toMap(ViewTypes::getSubTypeName, ViewTypes::getSubTypeId));
            log.debug("total anomaly event created is {}", anomalyEventList.size());

            anomalyEventList.forEach(c -> {
                if(!c.getAppIdList().isEmpty()) {
                    ApplicationSettings applicationSettings = cacheWrapper.getApplicationSettings(c.getAccountId(),
                            c.getAppId(0));
                    log.debug("processing anomaly for appid {}, accountId {}", c.getAppId(0), c.getAccountId());

                    if (Objects.isNull(applicationSettings)) {
                        forwarder.sendAnomalyOutputToSignalQueue(c);
                    } else {
                        int subTypeId = subTypeMap.getOrDefault(applicationSettings.getTypeName(), 413);
                        if (subTypeId == 413 || applicationSettings.getTypeName().equalsIgnoreCase("signal-detector")) {
                            forwarder.sendAnomalyOutputToSignalQueue(c);
                        }

                        if (subTypeId == 413 || applicationSettings.getTypeName().equalsIgnoreCase("event-correlation")) {
                            forwarder.sendAnomalyOutputToMLESignalQueue(c);
                        }
                    }
                }
            });
        } catch (Exception e) {
            log.error("Exception occurred while processing the violation event, Dropping data point {}", violatedEvent, e);
            metrics.updateViolatedEventProcessingErrors();
        } finally {
            metrics.updateSlowViolatedEvents(System.currentTimeMillis() - violatedEventProcessStartTime);
        }
    }

    public void validateAndProcessInputNorThresholdEvents(ThresholdProtos.Threshold threshold) {

        long norThresholdProcessStartTime = System.currentTimeMillis();
        try {
            GenericValidationObject<ThresholdProtos.Threshold> validateObject = thresholdDataProcess.validateNorThreshold(threshold);
            if (!validateObject.isValid()) {
                return;
            }
            threshold = validateObject.getProto();

            log.debug("Threshold proto verified successfully. Sending it to queue for further processing.");
            thresholdDataProcess.processAndSinkThresholdData(threshold);
        } catch (Exception e) {
            log.error("Exception occurred while processing the NOR threshold event, Dropping data point {}", threshold, e);
            metrics.updateNorThresholdProcessingErrors();
        } finally {
            metrics.updateSlowNorThresholdEvents(System.currentTimeMillis() - norThresholdProcessStartTime);
        }
    }

    public void validateAndProcessInputNorClosedThresholdEvents(ThresholdsClosedProtos.NORThresholdsClosed norThresholdsClosed) {
        long norClosedThresholdProcessStartTime = System.currentTimeMillis();
        try {
            GenericValidationObject<ThresholdsClosedProtos.NORThresholdsClosed> validateObject = closedNorThresholdDataProcess.validateNorClosedThreshold(norThresholdsClosed);
            if (!validateObject.isValid()) {
                return;
            }
            norThresholdsClosed = validateObject.getProto();

            log.debug("Nor Closed Threshold proto verified successfully. Sending it to queue for further processing.");
            closedNorThresholdDataProcess.processClosedThresholdData(norThresholdsClosed);
        } catch (Exception e) {
            log.error("Exception occurred while processing the NOR closed threshold event, Dropping data point {}", norThresholdsClosed, e);
            metrics.updateNorThresholdProcessingErrors();
        } finally {
            metrics.updateSlowNorClosedThresholdEvents(System.currentTimeMillis() - norClosedThresholdProcessStartTime);
        }
    }
}