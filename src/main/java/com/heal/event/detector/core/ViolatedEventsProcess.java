package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.AggregatedKpiProtos;
import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.heal.configuration.enums.OperationType;
import com.heal.configuration.pojos.*;
import com.heal.configuration.protbuf.AnomalySummaryProtos;
import com.heal.event.detector.exception.EventDetectorException;
import com.heal.event.detector.pojos.*;
import com.heal.event.detector.pojos.enums.Intervals;
import com.heal.event.detector.pojos.enums.ThresholdType;
import com.heal.event.detector.pojos.enums.ViolationEventType;
import com.heal.event.detector.repo.RedisUtilities;
import com.heal.event.detector.utility.Constants;
import com.heal.event.detector.utility.HealthMetrics;
import com.heal.event.detector.utility.StringUtils;
import com.heal.event.detector.utility.Utils;
import com.heal.event.detector.utility.cache.CacheWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class ViolatedEventsProcess {
    @Autowired
    GetViolatedData getViolatedData;
    @Autowired
    CacheWrapper cacheWrapper;

    @Autowired
    HealthMetrics metrics;
    @Value("${signal.severity.high:Severe}")
    String highSeveritySignal;

    @Value("${signal.severity.low:Default}")
    String lowSeveritySignal;

    @Value("${signal.severity.id.high:433}")
    String highSeverityIdSignal;

    @Value("${signal.severity.id.medium:432}")
    String mediumSeverityIdSignal;

    @Value("${signal.severity.id.low:431}")
    String lowSeverityIdSignal;

    @Value("${kpi.data.outoforder.mins:10}")
    int outOfOrderValue;

    public List<ViolatedData> processAggregatedKpiData(AggregatedKpiProtos.AggregatedKpi aggregatedKpi) {

        long st = System.currentTimeMillis();
        try {
            CompInstClusterDetails instanceDetails = cacheWrapper.getInstanceDetails(aggregatedKpi.getAccountId(), aggregatedKpi.getInstanceId());
            if (instanceDetails == null) {
                return Collections.emptyList();
            }

            //check and process if kpi is of config watch or file watch type
            KPIAgentMessageProtos.KPIAgentMessage.KpiData kpiData = aggregatedKpi.getKpiData();
            if (kpiData.getIsKpiGroup() && (kpiData.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.FileWatch
                    || kpiData.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.ConfigWatch)) {
                List<ViolatedData> watcherKpiViolatedEventList = processWatcherKpi(kpiData, aggregatedKpi);
                if (!watcherKpiViolatedEventList.isEmpty()) {
                    log.debug("Valid violation configuration found for KpiId [{}] mapped to " +
                                    "instance [{}] and accountId [{}]", aggregatedKpi.getKpiData().getKpiUid(), aggregatedKpi.getInstanceId(),
                            aggregatedKpi.getAccountId());
                    return watcherKpiViolatedEventList;
                } else {
                    log.error("No violated events found for [{}] KPI [{}] mapped to instance [{}] and" +
                                    " account [{}]. Hence Dropping this data point {}.", kpiData.getKpiType(),
                            aggregatedKpi.getKpiData().getKpiUid(), aggregatedKpi.getInstanceId(), aggregatedKpi.getAccountId(), aggregatedKpi);
                    return Collections.emptyList();
                }
            }

            MetaData metaData = new MetaData();
            Map<String, List<KpiViolationConfig>> overallThresholds = checkInstanceLevelThresholds(aggregatedKpi.getAccountId(), aggregatedKpi.getInstanceId(),
                    aggregatedKpi.getKpiData().getKpiUid(), aggregatedKpi.getKpiData().getGroupKpi());

            /* If a group kpi contains multiple attributes, and some attributes don't have
                instance level thresholds || if attributes list doesn't contain ALL attribute*/
            if (overallThresholds.isEmpty() || (!overallThresholds.containsKey(Constants.COMMON_ATTRIBUTE)
                    && overallThresholds.size() < aggregatedKpi.getKpiData().getGroupKpi().getPairsCount())) {
                Map<String, List<KpiViolationConfig>> serviceKpiViolationConfigMap = getServiceKpiViolationConfig(aggregatedKpi, instanceDetails.isCluster(), metaData);
                if (serviceKpiViolationConfigMap != null && !serviceKpiViolationConfigMap.isEmpty()) {
                    metaData.setViolationLevel("SERVICE");
                    overallThresholds.putAll(serviceKpiViolationConfigMap);
                }
            } else {
                metaData.setViolationLevel("INSTANCE");
            }

            if (overallThresholds.isEmpty()) {
                log.info("Violation configuration unavailable at instance level and service level for" +
                        " kpi [{}] mapped to instance [{}], account [{}].", aggregatedKpi.getKpiData().getKpiUid(), aggregatedKpi.getInstanceId(), aggregatedKpi.getAccountId());
                return Collections.emptyList();
            }

            return getViolationsData(aggregatedKpi, overallThresholds, metaData);
        } catch (Exception e) {
            log.debug("Exception while getting threshold details. Reason:- ", e);
            return Collections.emptyList();
        } finally {
            log.info("Time taken for process aggregated kpi data is {} ms.", (System.currentTimeMillis() - st));
        }

    }

    private Map<String, List<KpiViolationConfig>> checkInstanceLevelThresholds(String accountIdentifier, String instanceIdentifier,
                                                                         int kpiId, KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi groupKpi) {
        CompInstKpiEntity compInstKpiEntity = cacheWrapper.getInstanceKPIDetails(accountIdentifier, instanceIdentifier, kpiId);
        if (compInstKpiEntity == null) {
            return new HashMap<>();
        }

        Map<String, List<KpiViolationConfig>> kpiViolationConfigMap = compInstKpiEntity.getKpiViolationConfig();
        if (kpiViolationConfigMap == null || kpiViolationConfigMap.isEmpty()) {
            log.debug("Instance level kpi violation configuration unavailable for KPI [{}], instance [{}], account [{}]." +
                    " Proceeding for Service level threshold check.", kpiId, instanceIdentifier, accountIdentifier);
            return new HashMap<>();
        }

        kpiViolationConfigMap = kpiViolationConfigMap.entrySet().stream()
                .filter(e -> e.getValue().stream().allMatch(f -> f.getOperation() != null))
                .filter(e -> e.getValue().stream().allMatch(f -> f.getDefinedBy() == null || f.getDefinedBy().equalsIgnoreCase("USER")))
                .filter(e -> e.getValue().stream().allMatch(f -> f.getGenerateAnomaly() == 1))
                .filter(e -> {
                    if (groupKpi.getPairsCount() > 0) {
                        return groupKpi.containsPairs(e.getKey()) || e.getKey().equals(Constants.COMMON_ATTRIBUTE);
                    } else {
                        return true;
                    }
                })
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        if (kpiViolationConfigMap.isEmpty()) {
            log.debug("Instance-wise KPI attribute thresholds unavailable for KPI [{}], instance [{}], account [{}]." +
                    " Proceeding for Service level threshold check.", kpiId, instanceIdentifier, accountIdentifier);
            return new HashMap<>();
        }

        log.debug("Instance level thresholds available for attributes {} mapped to account [{}], componentInstance [{}] " +
                "and kpi [{}]", kpiViolationConfigMap.keySet().toArray(), accountIdentifier, instanceIdentifier, kpiId);

        return kpiViolationConfigMap;
    }

    private Map<String, List<KpiViolationConfig>> getServiceKpiViolationConfig(AggregatedKpiProtos.AggregatedKpi aggregatedKpi, boolean isCluster, MetaData metaData) {
        Map<String, List<KpiViolationConfig>> kpiViolationConfigMap = new HashMap<>();

        for (String srvIdentifier : aggregatedKpi.getServiceIdList()) {
            KpiDetails kpiDetails = cacheWrapper.getServiceKPIDetails(aggregatedKpi.getAccountId(), srvIdentifier, aggregatedKpi.getKpiData().getKpiUid());
            if (kpiDetails == null) {
                continue;
            }

            kpiViolationConfigMap = kpiDetails.getKpiViolationConfig();
            if (kpiViolationConfigMap == null || kpiViolationConfigMap.isEmpty()) {
                log.debug("Violation configuration unavailable for KPI [{}] mapped to service [{}] and" +
                        " account [{}]", aggregatedKpi.getKpiData().getKpiUid(), srvIdentifier, aggregatedKpi.getAccountId());
                continue;
            }

            kpiViolationConfigMap = kpiViolationConfigMap.entrySet().stream()
                    .filter(v -> v.getValue().stream().allMatch(w -> w.getOperation() != null))
                    .filter(v -> v.getValue().stream().allMatch(w -> w.getApplicableTo() != null))
                    .filter(v -> v.getValue().stream().allMatch(w -> w.getDefinedBy() == null || w.getDefinedBy().equalsIgnoreCase("USER")))
                    .filter(v -> v.getValue().stream().allMatch(w -> w.getGenerateAnomaly() == 1))
                    .filter(v -> v.getValue().stream().allMatch(w -> {
                        if (w.getStatus() == 0) {
                            log.debug("Service level static violation is disabled for KpiId" +
                                            " [{}] and attribute [{}] mapped to service [{}], account [{}]",
                                    aggregatedKpi.getKpiData().getKpiUid(), Constants.COMMON_ATTRIBUTE, srvIdentifier, aggregatedKpi.getAccountId());
                            return false;
                        }

                        if (isCluster && Constants.CLUSTERS.equalsIgnoreCase(w.getApplicableTo())) {
                            log.debug("Service level static violation configuration available for KpiId [{}] mapped to cluster [{}], service [{}], account [{}]",
                                    aggregatedKpi.getKpiData().getKpiUid(), aggregatedKpi.getInstanceId(), srvIdentifier, aggregatedKpi.getAccountId());
                            return checkLatestServiceKpiViolationConfig(aggregatedKpi, w, srvIdentifier, metaData);
                        } else if (!isCluster && Constants.INSTANCES.equalsIgnoreCase(w.getApplicableTo())) {
                            log.debug("Service level static violation configuration available for KpiId [{}] mapped to instance [{}], service [{}]," +
                                    " account [{}]", aggregatedKpi.getKpiData().getKpiUid(), aggregatedKpi.getInstanceId(), srvIdentifier, aggregatedKpi.getAccountId());
                            return checkLatestServiceKpiViolationConfig(aggregatedKpi, w, srvIdentifier, metaData);
                        }

                        log.debug("Service level static violation configuration unavailable for KpiId [{}] mapped to service [{}], account [{}]",
                                aggregatedKpi.getKpiData().getKpiUid(), srvIdentifier, aggregatedKpi.getAccountId());
                        return false;
                    }))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

            if (kpiViolationConfigMap.isEmpty()) {
                log.debug("Violation configuration unavailable for KPI [{}], service [{}] and account [{}]", aggregatedKpi.getKpiData().getKpiUid(),
                        srvIdentifier, aggregatedKpi.getAccountId());
                continue;
            }
        }

        if (kpiViolationConfigMap == null) {
            log.trace("Service level violation configuration unavailable.");
            return Collections.emptyMap();
        } else {
            log.trace("Service level violation configuration available.");
            return kpiViolationConfigMap;
        }
    }

    /**
     * Checks if the latest KPI violation configuration is applicable for the given aggregated KPI.
     * @param aggregatedKpi
     * @param violationConfiguration
     * @param srvIdentifier
     * @param metaData
     * @return true if the latest configuration is applicable, false otherwise.
     */
    private Boolean checkLatestServiceKpiViolationConfig(AggregatedKpiProtos.AggregatedKpi aggregatedKpi,
            KpiViolationConfig violationConfiguration, String srvIdentifier, MetaData metaData) {
        // GET latest KPI violation configuration
        try {
            Timestamp kpiCollectionTimestamp = Utils.getTimestamp(aggregatedKpi.getTimeInGMT(), 0);
            Timestamp currStartTimestamp = Utils.getTimestamp(violationConfiguration.getStartTime(), 0);
            Timestamp currEndTimestamp = Utils.getTimestamp(violationConfiguration.getEndTime(), 0);
            if (currStartTimestamp.before(kpiCollectionTimestamp) &&
                    (currEndTimestamp == null || currEndTimestamp.after(kpiCollectionTimestamp))) {
                log.debug("Latest KPI Violation configuration found for KPI [{}], service [{}]," +
                        " account [{}].", aggregatedKpi.getKpiData().getKpiUid(), srvIdentifier,
                        aggregatedKpi.getAccountId());
                metaData.setServiceIdentifier(srvIdentifier);
                return true;
            } else {
                log.debug("Latest KPI Violation configuration not found for KPI [{}], service [{}]," +
                        " account [{}]. Proceeding for KPI Violation configuration check with next service.",
                        aggregatedKpi.getKpiData().getKpiUid(), srvIdentifier, aggregatedKpi.getAccountId());
                return false;
            }
        } catch (Exception e) {
            log.error("Error while getting latest KPI violation configuration for KPI [{}] mapped" +
                    " to service [{}], account [{}]", aggregatedKpi.getKpiData().getKpiUid(), srvIdentifier,
                    aggregatedKpi.getAccountId(), e);
            return false;
        }
    }

    private List<ViolatedData> getViolationsData(AggregatedKpiProtos.AggregatedKpi aggregatedKpi, Map<String, List<KpiViolationConfig>> kpiViolationConfigMap, MetaData metaData) {
        KPIAgentMessageProtos.KPIAgentMessage.KpiData kpiData = aggregatedKpi.getKpiData();

        List<ViolatedData> violatedDataList = new ArrayList<>();

        if (kpiData.getIsKpiGroup()) {

            Map<String, String> attributeValueMap = kpiData.getGroupKpi().getPairsMap();
            log.debug("Processing group kpi [{}] mapped to instance [{}] and account [{}] for" +
                    " violation identification", aggregatedKpi.getKpiData().getKpiUid(), aggregatedKpi.getInstanceId(), aggregatedKpi.getAccountId());

            violatedDataList.addAll(attributeValueMap.entrySet().parallelStream()
                    .flatMap(e -> {
                        List<ViolatedData> vdList = violatedKpi(aggregatedKpi, e.getKey(), e.getValue(), kpiViolationConfigMap, metaData);
                        return vdList == null || vdList.isEmpty() ? Stream.empty() : vdList.stream();
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));

        } else {
            log.debug("Processing non group kpi [{}] mapped to instance [{}] and account [{}] for" +
                    " violation identification ", aggregatedKpi.getKpiData().getKpiUid(), aggregatedKpi.getInstanceId(), aggregatedKpi.getAccountId());

            violatedDataList = violatedKpi(aggregatedKpi, Constants.COMMON_ATTRIBUTE,
                    kpiData.getVal(), kpiViolationConfigMap, metaData);
        }

        return violatedDataList;
    }

    private List<ViolatedData> violatedKpi(AggregatedKpiProtos.AggregatedKpi aggregatedKpi, String kpiAttributeName, String kpiValue, Map<String, List<KpiViolationConfig>> violationProfiles, MetaData metaData) {
        Double coreKpiValue = Double.parseDouble(kpiValue);

        List<KpiViolationConfig> kpiViolationConfigList = violationProfiles.getOrDefault(kpiAttributeName,
                violationProfiles.get(Constants.COMMON_ATTRIBUTE));

        long eventTimeInEpochSec;
        try {
            eventTimeInEpochSec = Utils.dateStrToLocalDateTime(aggregatedKpi.getTimeInGMT()).toEpochSecond(ZoneOffset.UTC)*1000;
        } catch (EventDetectorException e) {
            log.error("Exception while converting KPI collection time into epoch seconds.");
            return null;
        }

        if (kpiViolationConfigList == null || kpiViolationConfigList.isEmpty()) {
            log.warn("Violation configuration unavailable for attribute [{}] or 'ALL' attribute of KPI [{}] mapped to" +
                    " instance [{}] and account [{}]", kpiAttributeName, aggregatedKpi.getKpiData().getKpiUid(), aggregatedKpi.getInstanceId(), aggregatedKpi.getAccountId());
            return null;
        }
        List<ViolatedData> violatedDataList = kpiViolationConfigList.stream()
                .filter(kv -> {
                    if (kv.getStatus() == 0) {
                        log.info("Static violation configuration is disabled for attribute [{}] of" +
                                " Kpi [{}] mapped to instance [{}] and account [{}]. Dropping the data" +
                                " point.", kpiAttributeName, aggregatedKpi.getInstanceId(), aggregatedKpi.getAccountId(), aggregatedKpi.getKpiData().getKpiUid());
                        return false;
                    }
                    if (kv.getMaxThreshold() == null || kv.getMinThreshold() == null || kv.getOperation() == null) {
                        log.error("Threshold data not found for attribute [{}] of" +
                                " Kpi [{}] mapped to instance [{}] and account [{}]. Dropping the data" +
                                " point.", kpiAttributeName, aggregatedKpi.getInstanceId(), aggregatedKpi.getAccountId(), aggregatedKpi.getKpiData().getKpiUid());
                        return false;
                    }
                    return true;
                })
                .map(kv -> {
                    Map<String, Double> thresholds = new HashMap<>();
                    thresholds.put(Constants.LOWER_THRESHOLD, kv.getMinThreshold());
                    thresholds.put(Constants.UPPER_THRESHOLD, kv.getMaxThreshold());

                    OperationType opType = Utils.applyThreshold(coreKpiValue, kv.getOperation(), thresholds);

                    log.debug("Valid violation configuration found for attribute [{}] of KpiId [{}], opType:{} mapped to instance" +
                            " [{}] and accountId [{}]", kpiAttributeName, aggregatedKpi.getKpiData().getKpiUid(), opType, aggregatedKpi.getInstanceId(), aggregatedKpi.getAccountId());

                    getViolatedData.handleViolationDetails(aggregatedKpi.getAccountId(), aggregatedKpi.getInstanceId(),
                            String.valueOf(aggregatedKpi.getKpiData().getKpiUid()), aggregatedKpi.getServiceIdList(),
                            kv, thresholds, metaData.getServiceIdentifier(), metaData.getViolationLevel(), opType,
                            kpiAttributeName, eventTimeInEpochSec, "SOR", false, null);

                    // If opType is null, it means the KPI value is within the thresholds, hence no violation
                    if (Objects.isNull(opType)) {
                        return null;
                    }
                    return createViolatedDataObject(aggregatedKpi, opType, String.valueOf(kv.getThresholdSeverityId()), kpiAttributeName, String.valueOf(coreKpiValue), thresholds, metaData);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        violatedDataList = getViolatedData.getViolatedDataForHighestSeverity(aggregatedKpi.getAccountId(),
                aggregatedKpi.getInstanceId(), String.valueOf(aggregatedKpi.getKpiData().getKpiUid()),
                aggregatedKpi.getServiceIdList(), violatedDataList, kpiAttributeName, metaData.getServiceIdentifier(),
                eventTimeInEpochSec, "SOR", false, null);

        return violatedDataList;
    }

    private List<ViolatedData> processWatcherKpi(KPIAgentMessageProtos.KPIAgentMessage.KpiData kpiData, AggregatedKpiProtos.AggregatedKpi aggregatedKpi) {
        if (kpiData.getWatcherKpiValue().getKeyValuePairMap().isEmpty()) {
            log.error("No data found in watcher kpi value's key value map.");
            return Collections.emptyList();
        }
        Map<String, KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi> watcherKpiMap = kpiData.getWatcherKpiValue().getKeyValuePairMap();

        return watcherKpiMap.entrySet().stream().map(k -> k.getValue().getPairsMap().entrySet().stream()
                        .filter(c -> {
                            if (kpiData.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.FileWatch) {
                                return !c.getValue().equalsIgnoreCase("Last Updated Time");
                            } else return true;
                        })
                        .map(d -> createViolatedDataObject(aggregatedKpi, OperationType.NOT_EQUALS,
                                lowSeverityIdSignal, d.getKey().trim(), d.getValue().trim(), new HashMap<>(),
                                MetaData.builder()
                                        .violationLevel("INSTANCE")
                                        .fileName(k.getKey().trim())
                                        .build()))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .flatMap(Collection::parallelStream)
                .collect(Collectors.toList());

    }

    private ViolatedData createViolatedDataObject(AggregatedKpiProtos.AggregatedKpi aggregatedKpi, OperationType operationType,
                                                  String thresholdSeverityId, String kpiAttributeName, String kpiVal, Map<String, Double> thresholds, MetaData metaData) {

        KPIAgentMessageProtos.KPIAgentMessage.KpiData kpiInfo = aggregatedKpi.getKpiData();
        String timeInGMT = Utils.getKey(kpiInfo.getTimeInGMT(), Intervals.Minutely); //remove seconds

        long violatedTimeInEpochSec;
        try {
            violatedTimeInEpochSec = Utils.dateStrToLocalDateTime(timeInGMT).toEpochSecond(ZoneOffset.UTC);
        } catch (EventDetectorException e) {
            log.error("Exception while converting KPI collection time into epoch seconds.");
            return null;
        }

        BasicKPIStateInfo kpiStateInfo = getKpiDetails(aggregatedKpi.getAccountId(), aggregatedKpi.getInstanceId(),
                aggregatedKpi.getKpiData().getKpiUid(), kpiAttributeName, metaData.getViolationLevel(),
                metaData.getViolationLevel().equals("SERVICE") ? metaData.getServiceIdentifier() : null, aggregatedKpi.getKpiData().getKpiType());
        if (kpiStateInfo == null) {
            log.warn("Kpi details not found. Reason: Invalid KpiInfo. Details: {}", aggregatedKpi);
            return null;
        }
        if (!kpiStateInfo.getGenerateAnomaly()) {
            log.info("Kpi violation check is ignored. Reason: Generate Anomaly is set to false. KPI details: [{}]", aggregatedKpi);
            return null;
        }

        List<String> validServices = aggregatedKpi.getServiceIdList();

        if (!kpiStateInfo.getIsMaintenanceExcluded()) {
            if (getViolatedData.isInstanceMaintenanceWindowOn(aggregatedKpi.getInstanceId(), aggregatedKpi.getAccountId(), aggregatedKpi.getTimeInGMT())) {
                log.info("Kpi violation check is ignored. Reason: Instance is under maintenance. KPI details: [{}]", aggregatedKpi);
                return null;
            }
            validServices = getViolatedData.getSvsNotUnderMaintenanceWindow(aggregatedKpi.getServiceIdList(), aggregatedKpi.getAccountId(), aggregatedKpi.getTimeInGMT());
            if (validServices.isEmpty()) {
                log.info("Kpi violation check is ignored. Reason: Service(s) is under maintenance. KPI details: [{}]", aggregatedKpi);
                return null;
            }
        }

        ViolatedData violatedData = new ViolatedData(aggregatedKpi.getAccountId(), aggregatedKpi.getApplicationIdList());

        String thresholdType = ThresholdType.STATIC.getThresholdType();

        violatedData.setThresholdType(thresholdType);
        violatedData.setViolationFor(thresholdType.equalsIgnoreCase("Static") ? "SOR" : "NOR");
        violatedData.setTimezoneOffsetInSeconds(aggregatedKpi.getTimeZoneOffsetInSec());
        violatedData.setViolationTime(violatedTimeInEpochSec * 1000);
        violatedData.setServiceList(metaData.getServiceIdentifier() == null ? validServices : Collections.singletonList(metaData.getServiceIdentifier()));
        violatedData.setValue(kpiVal);
        violatedData.setEventType(ViolationEventType.KPI_VIOLATION);
        violatedData.setKpiId(String.valueOf(kpiInfo.getKpiUid()));
        violatedData.setInstanceId(aggregatedKpi.getInstanceId());
        violatedData.setKpiAttribute(kpiAttributeName);
        violatedData.setOperationType(operationType.getOperationType());
        violatedData.setThresholdSeverity(thresholdSeverityId);
        violatedData.setKpiType(kpiInfo.getKpiType());
        violatedData.setDisplayAttributeName(kpiStateInfo.getAttributeDisplayName() == null ?
                kpiAttributeName : kpiStateInfo.getAttributeDisplayName());
        violatedData.setIsInfo(kpiStateInfo.getIsInfo());
        violatedData.setMaintenanceExcluded(kpiStateInfo.getIsMaintenanceExcluded());
        violatedData.setKpiViolationTime(timeInGMT);

        Map<String, Double> orderedThreshold = new HashMap<>();
        if (kpiInfo.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core
                || kpiInfo.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Availability) {
            orderedThreshold = Utils.getThresholdBasedOnOperationType(thresholds, operationType.getOperationType());
            if (orderedThreshold == null || orderedThreshold.isEmpty()) {
                violatedData.setThresholds(thresholds);
                return violatedData;
            }
        }

        Map<String, String> allMetaData = new HashMap<>(aggregatedKpi.getMetaDataMap());
        allMetaData.put("isGroupKpi", String.valueOf(kpiInfo.getIsKpiGroup()));
        if (kpiInfo.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core) {

            allMetaData.put("anomalyScore", Utils.getAnomalyScore(orderedThreshold, Double.parseDouble(kpiVal)));

        } else if (kpiInfo.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Availability) {

            if (violatedData.getThresholdSeverity().equals(highSeverityIdSignal)) {
                allMetaData.put("anomalyScore", "1");
            } else if (violatedData.getThresholdSeverity().equals(mediumSeverityIdSignal)) {
                allMetaData.put("anomalyScore", "0.7");
            } else if (violatedData.getThresholdSeverity().equals(lowSeverityIdSignal)) {
                allMetaData.put("anomalyScore", "0.5");
            }
        }

        if (!StringUtils.isEmpty(aggregatedKpi.getAgentId())) {
            allMetaData.put("agentUid", aggregatedKpi.getAgentId());
        }

        allMetaData.put("violationLevel", metaData.getViolationLevel());

        if (kpiInfo.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.FileWatch
                || kpiInfo.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.ConfigWatch) {
            allMetaData.put("fileName", metaData.getFileName());
        }
        if (metaData.getViolationLevel().equalsIgnoreCase("SERVICE")) {
            allMetaData.put("serviceIdentifier", metaData.getServiceIdentifier());
        }
        violatedData.setMetaData(allMetaData);
        violatedData.setThresholds(orderedThreshold);

        return violatedData;
    }

    private BasicKPIStateInfo getKpiDetails(String accountIdentifier, String instanceIdentifier, int kpiId, String attributeName, String violationLevel, String serviceIdentifier, KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType kpiType) {
        BasicKPIStateInfo basicKPIStateInfo = new BasicKPIStateInfo();

        try {
            CompInstKpiEntity kpiDetails = cacheWrapper.getInstanceKPIDetails(accountIdentifier, instanceIdentifier, kpiId);
            if (kpiDetails == null) {
                return null;
            }

            if (kpiDetails.getIsGroup()) {
                if (kpiDetails.getAttributeValues() != null) {
                    basicKPIStateInfo.setAttributeDisplayName(kpiDetails.getAttributeValues().getOrDefault(attributeName,
                            attributeName));
                }
            }
            basicKPIStateInfo.setIsInfo(kpiDetails.getIsInfo());

            if ((kpiType == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.FileWatch
                    || kpiType == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.ConfigWatch) && (kpiDetails.getKpiViolationConfig() == null || kpiDetails.getKpiViolationConfig().get(attributeName) == null)) {
                basicKPIStateInfo.setIsMaintenanceExcluded(false);
                basicKPIStateInfo.setGenerateAnomaly(true);
            } else if (violationLevel.equals("INSTANCE")) {
                basicKPIStateInfo.setIsMaintenanceExcluded(kpiDetails.getKpiViolationConfig().containsKey(attributeName) && kpiDetails.getKpiViolationConfig().get(attributeName).get(0).getExcludeMaintenance() == 1);
                basicKPIStateInfo.setGenerateAnomaly(kpiDetails.getKpiViolationConfig().containsKey(attributeName) && kpiDetails.getKpiViolationConfig().get(attributeName).get(0).getGenerateAnomaly() == 1);

            } else if (violationLevel.equals("SERVICE") && serviceIdentifier != null) {
                KpiDetails serviceKpiDetails = cacheWrapper.getServiceKPIDetails(accountIdentifier, serviceIdentifier, kpiId);
                if (serviceKpiDetails == null) {
                    return null;
                }
                basicKPIStateInfo.setIsMaintenanceExcluded(serviceKpiDetails.getKpiViolationConfig().get(attributeName).get(0).getExcludeMaintenance() == 1);
                basicKPIStateInfo.setGenerateAnomaly(serviceKpiDetails.getKpiViolationConfig().get(attributeName).get(0).getGenerateAnomaly() == 1);
            } else return null;

        } catch (Exception e) {
            log.error("Error while finding basic kpi state details : Account:{}, Instance:{}, kpiId:{}, attribute:{}",
                    accountIdentifier, instanceIdentifier, kpiId, attributeName, e);
            return null;
        }
        return basicKPIStateInfo;
    }

    public GenericValidationObject<AggregatedKpiProtos.AggregatedKpi> validateAggregatedKpiData(AggregatedKpiProtos.AggregatedKpi aggregatedKpi) {
        GenericValidationObject<AggregatedKpiProtos.AggregatedKpi> result =
                GenericValidationObject.<AggregatedKpiProtos.AggregatedKpi>builder()
                        .proto(aggregatedKpi)
                        .isValid(false)
                        .build();

        if (aggregatedKpi == null) {
            log.error("Validation of aggregated kpi data failed. Reason: Data received is null." +
                    "Hence it will not be processed further.");
            metrics.updateAggregatedKpiProcessingErrors();
            return result;
        }

        if (aggregatedKpi.getSerializedSize() == 0) {
            log.error("Validation of aggregated kpi data failed. Reason: Data received is either invalid or undefined." +
                    "Hence it will not be processed further.");
            metrics.updateAggregatedKpiProcessingErrors();
            return result;
        }

        if (StringUtils.isEmpty(aggregatedKpi.getAccountId())) {
            log.error("Validation of aggregated kpi data failed. Reason: Account identifier is " +
                    "either invalid or undefined. Hence it will not be be processed further.");
            metrics.updateAggregatedKpiProcessingErrors();
            return result;
        }

        if (StringUtils.isEmpty(aggregatedKpi.getInstanceId())) {
            log.error("Validation of aggregated kpi data failed. Reason: Instance identifier is " +
                    "either invalid or undefined. Hence it will not be be processed further.");
            metrics.updateAggregatedKpiProcessingErrors();
            return result;
        }

        if (aggregatedKpi.getServiceIdList().isEmpty()) {
            log.error("Validation of aggregated kpi data failed. Reason: Service identifier list is " +
                    "empty. Hence it will not be be processed further.");
            metrics.updateAggregatedKpiProcessingErrors();
            return result;
        }

        List<String> nonEmptyServiceIdList = aggregatedKpi.getServiceIdList().stream()
                .filter(c -> !StringUtils.isEmpty(c)).collect(Collectors.toList());
        if (nonEmptyServiceIdList.isEmpty()) {
            log.error("Validation of aggregated kpi data failed. Reason: Service identifier is " +
                    "either invalid or undefined. Hence it will not be be processed further.");
            metrics.updateAggregatedKpiProcessingErrors();
            return result;
        }

        if (nonEmptyServiceIdList.size() < aggregatedKpi.getServiceIdList().size()) {
            aggregatedKpi = aggregatedKpi.toBuilder().clearServiceId().addAllServiceId(nonEmptyServiceIdList).build();
            result.setProto(aggregatedKpi);
        }

        if (aggregatedKpi.getKpiData().getKpiUid() <= 0) {
            log.error("Validation of aggregated kpi data failed. Reason: Kpi id is " +
                    "either invalid or undefined. Hence it will not be be processed further.");
            metrics.updateAggregatedKpiProcessingErrors();
            return result;
        }

        if (aggregatedKpi.getApplicationIdList().isEmpty()) {
            log.error("Validation of aggregated kpi data failed. Reason: Application identifier list is " +
                    "empty. Hence it will not be be processed further.");
            metrics.updateAggregatedKpiProcessingErrors();
            return result;
        }

        List<String> nonEmptyAppIdList = aggregatedKpi.getApplicationIdList().stream()
                .filter(c -> !StringUtils.isEmpty(c)).collect(Collectors.toList());
        if (nonEmptyAppIdList.isEmpty()) {
            log.error("Validation of aggregated kpi data failed. Reason: Application identifier is " +
                    "either invalid or undefined. Hence it will not be be processed further.");
            metrics.updateAggregatedKpiProcessingErrors();
            return result;
        }

        if (nonEmptyAppIdList.size() < aggregatedKpi.getApplicationIdList().size()) {
            aggregatedKpi = aggregatedKpi.toBuilder().clearApplicationId().addAllApplicationId(nonEmptyAppIdList).build();
            result.setProto(aggregatedKpi);
        }

        if (StringUtils.isEmpty(aggregatedKpi.getKpiData().getKpiName())) {
            log.error("Validation of aggregated kpi data failed. Reason: Kpi name is " +
                    "either invalid or undefined. Hence it will not be be processed further.");
            metrics.updateAggregatedKpiProcessingErrors();
            return result;
        }

        if (aggregatedKpi.getKpiData().getIsKpiGroup()) {
            if ((aggregatedKpi.getKpiData().getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core
                    || aggregatedKpi.getKpiData().getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Availability)
                    && aggregatedKpi.getKpiData().getGroupKpi().getPairsCount() == 0) {
                log.error("Validation of aggregated kpi data failed. Reason: Invalid value for Group KPI id [{}] mapped" +
                                " to instance [{}] and account [{}]. Dropping the data point.",
                        aggregatedKpi.getKpiData().getKpiUid(), aggregatedKpi.getInstanceId(), aggregatedKpi.getAccountId());
                metrics.updateAggregatedKpiProcessingErrors();
                return result;
            } else if ((aggregatedKpi.getKpiData().getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.ConfigWatch
                    || aggregatedKpi.getKpiData().getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.FileWatch)
                    && !aggregatedKpi.getKpiData().hasWatcherKpiValue()) {
                log.error("Validation of aggregated kpi data failed. Reason: Invalid value for Config/File Watch KPI id [{}] mapped" +
                                " to instance [{}] and account [{}]. Dropping the data point.",
                        aggregatedKpi.getKpiData().getKpiUid(), aggregatedKpi.getInstanceId(), aggregatedKpi.getAccountId());
                metrics.updateAggregatedKpiProcessingErrors();
                return result;
            }
        } else if (!StringUtils.isNumeric(aggregatedKpi.getKpiData().getVal())) {
            log.error("Validation of aggregated kpi data failed. Reason: Invalid value [{}] for KPI id [{}] mapped" +
                            " to instance [{}] and account [{}]. Dropping the data point.", aggregatedKpi.getKpiData().getVal(),
                    aggregatedKpi.getKpiData().getKpiUid(), aggregatedKpi.getInstanceId(), aggregatedKpi.getAccountId());
            metrics.updateAggregatedKpiProcessingErrors();
            return result;
        }

        if (Utils.isOutOfOrder(aggregatedKpi.getKpiData().getTimeInGMT(), outOfOrderValue)) {
            log.error("Validation of aggregated kpi data failed. Reason: OutOfOrder time {}, outOfOrderValue:{} for KPI id {}, instance {} and account {}. Dropping the data point.",
                    aggregatedKpi.getKpiData().getTimeInGMT(), outOfOrderValue, aggregatedKpi.getKpiData().getKpiUid(), aggregatedKpi.getInstanceId(), aggregatedKpi.getAccountId());
            metrics.updateAggregatedKpiProcessingErrors();
            return result;
        }

        log.debug("Static Aggregated KPI Proto data is validated successfully.");
        result.setValid(true);
        return result;
    }
}