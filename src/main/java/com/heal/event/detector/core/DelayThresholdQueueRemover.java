package com.heal.event.detector.core;

import com.heal.event.detector.pojos.DelayThresholdQueue;
import com.heal.event.detector.utility.LocalCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DelayThresholdQueueRemover {

    @Autowired
    LocalCache localCache;

    @Scheduled(initialDelayString = "${delay.queue.remove.schedule.initial.delay:2}", fixedRateString = "${delay.queue.remove.schedule.interval:60}", timeUnit = TimeUnit.SECONDS)
    public void removeFromDelayQueue() {
        while (localCache.delayThresholdQueue.peek() != null) {
            DelayThresholdQueue delayThresholdQueue = localCache.delayThresholdQueue.poll();
            log.trace("Delay Queue Request: " + delayThresholdQueue);
            if (delayThresholdQueue == null) {
                return;
            }

            if (delayThresholdQueue.getType().equalsIgnoreCase("instance")) {
                localCache.instanceKpiThresholdsConcurrentHashMap.remove(delayThresholdQueue.getKey());
            } else if (delayThresholdQueue.getType().equalsIgnoreCase("transaction")) {
                localCache.transactionKpiThresholdsConcurrentHashMap.remove(delayThresholdQueue.getKey());
            }

            log.trace("Removed key {} from cache successfully.", delayThresholdQueue.getKey());
        }
    }

}
