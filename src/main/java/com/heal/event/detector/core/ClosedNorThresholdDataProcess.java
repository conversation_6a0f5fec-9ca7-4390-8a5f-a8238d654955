package com.heal.event.detector.core;

import com.heal.configuration.entities.BasicInstanceBean;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.InstanceKpiThresholds;
import com.heal.configuration.pojos.opensearch.TransactionKpiThresholds;
import com.heal.configuration.protbuf.ThresholdsClosedProtos;
import com.heal.event.detector.exception.EventDetectorException;
import com.heal.event.detector.pojos.GenericValidationObject;
import com.heal.event.detector.pojos.InstanceKPIThresholdsPojo;
import com.heal.event.detector.pojos.TxnKPIThresholdsPojo;
import com.heal.event.detector.repo.OpenSearchRepo;
import com.heal.event.detector.utility.*;
import com.heal.event.detector.utility.cache.CacheWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ClosedNorThresholdDataProcess {

    @Autowired
    HealthMetrics metrics;
    @Autowired
    CacheWrapper cacheWrapper;
    @Autowired
    OpenSearchRepo openSearchRepo;
    @Value("${closed.thresholds.duration.days:30}")
    private int durationInDays;

    public void processClosedThresholdData(ThresholdsClosedProtos.NORThresholdsClosed norThresholdsClosed) throws EventDetectorException {
        List<TxnKPIThresholdsPojo> txnKPIThresholdsList = new ArrayList<>();
        List<InstanceKPIThresholdsPojo> instanceKPIThresholdsList = new ArrayList<>();

        Timestamp endTimeToBeUpdated = Utils.getTimestamp(norThresholdsClosed.getInstanceThreshold(0).getTimeInGMT(), 0);
        String accountIdentifier = norThresholdsClosed.getInstanceThreshold(0).getAccountId();

        List<ThresholdsClosedProtos.InstanceThreshold> instanceThresholdList = norThresholdsClosed.getInstanceThresholdList();

        for (ThresholdsClosedProtos.InstanceThreshold threshold : instanceThresholdList) {

            if (StringUtils.isEmpty(threshold.getTransactionId())) {
                InstanceKPIThresholdsPojo instanceKPIThresholdsPojo = validateInstanceKPIDetails(threshold, accountIdentifier);
                instanceKPIThresholdsList.add(instanceKPIThresholdsPojo);
            } else {
                TxnKPIThresholdsPojo txnKPIThresholdsPojo = validateTxnKPIDetails(threshold, accountIdentifier);
                txnKPIThresholdsList.add(txnKPIThresholdsPojo);
            }
        }

        if (!StringUtils.isEmpty(instanceKPIThresholdsList)) {
            log.trace("List of instance KPI thresholds that passed validation and will get updated in OS are {}", instanceKPIThresholdsList);
            updateInstanceKPIClosedThresholdsInOS(endTimeToBeUpdated.getTime(), accountIdentifier, instanceKPIThresholdsList);
        }

        if (!StringUtils.isEmpty(txnKPIThresholdsList)) {
            log.trace("List of transaction KPI thresholds that passed validation and will get updated in OS are {}", txnKPIThresholdsList);
            updateTxnKPIClosedThresholdsInOS(endTimeToBeUpdated.getTime(), accountIdentifier, txnKPIThresholdsList);
        }
    }

    private InstanceKPIThresholdsPojo validateInstanceKPIDetails(ThresholdsClosedProtos.InstanceThreshold threshold, String accountIdentifier) {

        Application applicationByIdentifier = cacheWrapper.getApplicationByIdentifier(accountIdentifier, threshold.getApplicationId());
        if (applicationByIdentifier == null) {
            log.error("Could not find application details for application {} of account {} redis cache. Therefore, ignoring this iteration and proceeding to the next", threshold.getApplicationId(), accountIdentifier);
            metrics.updateNorClosedThresholdProcessingErrors();
            return null;
        }

        if (applicationByIdentifier.getStatus() == 0) {
            log.error("The application {} of account {} is inactive. Therefore, ignoring this iteration and proceeding to the next", threshold.getApplicationId(), accountIdentifier);
            metrics.updateNorClosedThresholdProcessingErrors();
            return null;
        }

        List<BasicEntity> servicesMappedToApplication = cacheWrapper.getServicesMappedToApplication(accountIdentifier, threshold.getApplicationId());
        if (servicesMappedToApplication == null || servicesMappedToApplication.isEmpty()) {
            log.error("Application service details for application {} of account {} could not be found in the redis cache. Therefore, ignoring this iteration and proceeding to the next", threshold.getApplicationId(), accountIdentifier);
            metrics.updateNorClosedThresholdProcessingErrors();
            return null;
        }

        Optional<BasicEntity> serviceApplicationMapping = servicesMappedToApplication.parallelStream().filter(f -> f.getIdentifier().equalsIgnoreCase(threshold.getServiceId()) && f.getStatus() == 1).findAny();
        if (!serviceApplicationMapping.isPresent()) {
            log.error("Could not Service details are not present in service application mapping redis key for application {} of account {}. Therefore, ignoring this iteration and proceeding to the next", threshold.getApplicationId(), accountIdentifier);
            metrics.updateNorClosedThresholdProcessingErrors();
            return null;
        }

        List<BasicInstanceBean> serviceInstances = cacheWrapper.getServiceInstances(accountIdentifier, threshold.getServiceId());
        if (serviceInstances == null || serviceInstances.isEmpty()) {
            log.error("Could not find service instances detail in redis cache for service {} of account {}. Therefore, ignoring this iteration and proceeding to the next", threshold.getServiceId(), accountIdentifier);
            metrics.updateNorClosedThresholdProcessingErrors();
            return null;
        }

        Optional<BasicInstanceBean> serviceInstanceMapping = serviceInstances.parallelStream().filter(f -> f.getIdentifier().equalsIgnoreCase(threshold.getInstanceId()) && f.getStatus() == 1).findAny();
        if (!serviceInstanceMapping.isPresent()) {
            log.error("Could not find instance details from service instances mapping redis key for service {} of account {}. Therefore, ignoring this iteration and proceeding to the next", threshold.getServiceId(), accountIdentifier);
            metrics.updateNorClosedThresholdProcessingErrors();
            return null;
        }

        List<CompInstKpiEntity> instanceKPIDetails = cacheWrapper.getInstanceKPIDetails(accountIdentifier, threshold.getInstanceId());
        if (instanceKPIDetails.isEmpty()) {
            log.error("Could not find KPI details in redis cache for instance {} of account {}. Therefore, ignoring this iteration and proceeding to the next", threshold.getInstanceId(), accountIdentifier);
            metrics.updateNorClosedThresholdProcessingErrors();
            return null;
        }

        List<Integer> instanceKpisFromRedis = instanceKPIDetails
                .parallelStream()
                .filter(f -> f.getStatus() == 1)
                .map(BasicKpiEntity::getId)
                .collect(Collectors.toList());

        List<ThresholdsClosedProtos.KPIInfo> kpiInfoList = new ArrayList<>(threshold.getKpiInfoList());

        kpiInfoList.removeIf(kpiInfo -> !instanceKpisFromRedis.contains(Integer.parseInt(kpiInfo.getKpiId())));

        List<String> existingActiveKPIs = kpiInfoList
                .parallelStream()
                .map(ThresholdsClosedProtos.KPIInfo::getKpiId)
                .collect(Collectors.toList());

        if (existingActiveKPIs.isEmpty()) {
            log.error("None of the kpis obtained from protobuf are present in or active in redis cache for instance {} of account {}. Therefore, ignoring this iteration and proceeding to the next", threshold.getInstanceId(), accountIdentifier);
            metrics.updateNorClosedThresholdProcessingErrors();
            return null;
        }

        log.trace("The list of instance KPIs from proto that are present in the redis and are active are {}.", existingActiveKPIs);

        Map<String, List<String>> instanceKpisMap = new HashMap<>();
        instanceKpisMap.put(threshold.getInstanceId(), existingActiveKPIs);

        return InstanceKPIThresholdsPojo.builder()
                .instanceKPIsMap(instanceKpisMap)
                .serviceIdentifier(threshold.getServiceId())
                .applicationIdentifier(threshold.getApplicationId())
                .build();
    }

    private TxnKPIThresholdsPojo validateTxnKPIDetails(ThresholdsClosedProtos.InstanceThreshold threshold, String accountIdentifier) {

        com.heal.configuration.pojos.Service serviceDetails = cacheWrapper.getServiceDetails(accountIdentifier, threshold.getServiceId());
        if (serviceDetails == null) {
            log.error("Could not find service details in redis cache for service id {} of account {}", threshold.getServiceId(), accountIdentifier);
            metrics.updateNorClosedThresholdProcessingErrors();
            return null;
        }

        if (serviceDetails.getStatus() == 0) {
            log.error("The service {} of account {} is inactive. Therefore, ignoring this iteration and proceeding to the next", threshold.getServiceId(), accountIdentifier);
            metrics.updateNorClosedThresholdProcessingErrors();
            return null;
        }

        List<BasicTransactionEntity> serviceWiseTransaction = cacheWrapper.getServiceWiseTransaction(accountIdentifier, threshold.getServiceId());
        if (serviceWiseTransaction == null || serviceWiseTransaction.isEmpty()) {
            log.error("Could not find service wise transactions detail in redis cache for service {} of account {}", threshold.getServiceId(), accountIdentifier);
            metrics.updateNorClosedThresholdProcessingErrors();
            return null;
        }

        Optional<BasicTransactionEntity> serviceTransactionMapping = serviceWiseTransaction.parallelStream().filter(f -> f.getIdentifier().equalsIgnoreCase(threshold.getTransactionId()) && f.getStatus() == 1).findAny();
        if (!serviceTransactionMapping.isPresent()) {
            log.error("Transaction details are not present in service transaction mapping redis key for service {} of account {}", threshold.getServiceId(), accountIdentifier);
            metrics.updateNorClosedThresholdProcessingErrors();
            return null;
        }

        List<ComponentKpiEntity> componentKPIs = cacheWrapper.getComponentKPIs(accountIdentifier, Constants.TRANSACTION_COMPONENT_NAME);
        if (componentKPIs == null || componentKPIs.isEmpty()) {
            log.error("Could not find component kpi details from redis cache for component name {} of account {}", Constants.TRANSACTION_COMPONENT_NAME, accountIdentifier);
            metrics.updateNorClosedThresholdProcessingErrors();
            return null;
        }

        List<Integer> txnKpisFromRedis = componentKPIs
                .parallelStream()
                .filter(f -> f.getStatus() == 1)
                .map(BasicKpiEntity::getId)
                .collect(Collectors.toList());

        List<ThresholdsClosedProtos.KPIInfo> kpiInfoList = new ArrayList<>(threshold.getKpiInfoList());

        kpiInfoList.removeIf(kpiInfo -> !txnKpisFromRedis.contains(Integer.parseInt(kpiInfo.getKpiId())));

        List<String> existingActiveKPIs = kpiInfoList
                .parallelStream()
                .map(ThresholdsClosedProtos.KPIInfo::getKpiId)
                .collect(Collectors.toList());

        log.trace("The list of transaction KPIs from proto that are present in the redis are {}.", existingActiveKPIs);

        Map<String, List<String>> txnKpisMap = new HashMap<>();
        txnKpisMap.put(threshold.getTransactionId(), existingActiveKPIs);

        return TxnKPIThresholdsPojo.builder()
                .txnKpisMap(txnKpisMap)
                .serviceIdentifier(threshold.getServiceId())
                .build();
    }

    private void updateInstanceKPIClosedThresholdsInOS(long toTime, String accountIdentifier,
                                                       List<InstanceKPIThresholdsPojo> instanceKPIThresholdsList) {

        log.trace("Computed from time will be exactly {} days before the time [{}] obtained from proto", durationInDays, DateTimeUtil.getTimeInGMT(toTime));

        long fromTime = DateTimeUtil.computeTimeBeforeNDaysFromToTime(durationInDays, toTime);

        log.trace("Computed from time {}, to time  {}", fromTime, toTime);

        Map<String, List<String>> instanceKPIsMap = new HashMap<>();
        Set<String> appIds = new HashSet<>();
        Set<String> serviceIds = new HashSet<>();
        Set<String> kpiIds = new HashSet<>();
        instanceKPIThresholdsList.forEach(instanceKPIThresholdsPojo -> {
            appIds.add(instanceKPIThresholdsPojo.getApplicationIdentifier());
            serviceIds.add(instanceKPIThresholdsPojo.getServiceIdentifier());
            kpiIds.addAll(instanceKPIThresholdsPojo.getInstanceKPIsMap().values().stream().flatMap(Collection::parallelStream).collect(Collectors.toSet()));
            instanceKPIsMap.putAll(instanceKPIThresholdsPojo.getInstanceKPIsMap());
        });

        List<InstanceKpiThresholds> instanceLevelThresholdDetails = openSearchRepo.getInstanceLevelThresholdDetails(accountIdentifier, appIds, serviceIds, fromTime, toTime);
        if (instanceLevelThresholdDetails.isEmpty()) {
            log.error("No documents related to instance threshold details obtained from opensearch for Applications {}, Services {} of account {}", appIds, serviceIds, accountIdentifier);
            return;
        }

        List<InstanceKpiThresholds> updatedInstanceLevelThresholdDetails = instanceLevelThresholdDetails.parallelStream()
                .filter(f -> instanceKPIsMap.containsKey(f.getInstanceId()))
                .filter(f -> kpiIds.contains(String.valueOf(f.getKpiId())))
                .peek(m -> m.setEndTime(toTime))
                .collect(Collectors.toList());

        openSearchRepo.updateInstanceKpiClosedThresholds(accountIdentifier, updatedInstanceLevelThresholdDetails);
    }

    private void updateTxnKPIClosedThresholdsInOS(long toTime, String accountIdentifier, List<TxnKPIThresholdsPojo> txnKPIThresholdsList) {

        log.trace("Computed from time will be exactly {} days before the time {} obtained from proto", durationInDays, DateTimeUtil.getTimeInGMT(toTime));

        long fromTime = DateTimeUtil.computeTimeBeforeNDaysFromToTime(durationInDays, toTime);

        log.trace("Computed from time {}, to time  {}", fromTime, toTime);

        Set<String> kpiIds = new HashSet<>();
        Set<String> serviceIds = new HashSet<>();
        Map<String, List<String>> txnKPIsMap = new HashMap<>();

        txnKPIThresholdsList.forEach(txnKPIThresholdsPojo -> {
            kpiIds.addAll(txnKPIThresholdsPojo.getTxnKpisMap().values().stream().flatMap(Collection::parallelStream).collect(Collectors.toSet()));
            serviceIds.add(txnKPIThresholdsPojo.getServiceIdentifier());
            txnKPIsMap.putAll(txnKPIThresholdsPojo.getTxnKpisMap());
        });

        List<TransactionKpiThresholds> transactionThresholdDetails = openSearchRepo.getTransactionThresholdDetails(accountIdentifier, serviceIds, fromTime, toTime);

        if (transactionThresholdDetails.isEmpty()) {
            log.error("No documents related to transaction threshold details obtained from opensearch for services {} of account {}", serviceIds, accountIdentifier);
            return;
        }

        List<TransactionKpiThresholds> updatedTransactionThresholdDetails = transactionThresholdDetails
                .parallelStream()
                .filter(f -> txnKPIsMap.containsKey(f.getTransactionId()))
                .filter(f -> kpiIds.contains(String.valueOf(f.getKpiId())))
                .peek(m -> m.setEndTime(toTime))
                .collect(Collectors.toList());

        openSearchRepo.updateTxnKpiClosedThresholds(accountIdentifier, updatedTransactionThresholdDetails);
    }

    public GenericValidationObject<ThresholdsClosedProtos.NORThresholdsClosed> validateNorClosedThreshold(ThresholdsClosedProtos.NORThresholdsClosed norThresholdsClosed) {
        GenericValidationObject<ThresholdsClosedProtos.NORThresholdsClosed> result = GenericValidationObject.<ThresholdsClosedProtos.NORThresholdsClosed>builder()
                .isValid(false)
                .build();

        if (norThresholdsClosed == null) {
            log.error("Validation of NOR closed threshold data failed. Reason: Data received is null." +
                    "Hence it will not be processed further.");
            metrics.updateNorClosedThresholdProcessingErrors();
            return result;
        }

        if (norThresholdsClosed.getSerializedSize() == 0) {
            log.error("Validation of NOR closed threshold data failed. Reason: Data received is either invalid or undefined." +
                    "Hence it will not be processed further.");
            metrics.updateNorClosedThresholdProcessingErrors();
            return result;
        }

        List<ThresholdsClosedProtos.InstanceThreshold> instanceThresholdList = norThresholdsClosed.getInstanceThresholdList();
        List<ThresholdsClosedProtos.InstanceThreshold> finalInstanceThresholdList = new ArrayList<>();

        if (StringUtils.isEmpty(instanceThresholdList)) {
            log.debug("Validation of NOR closed threshold data failed. Reason: Instance thresholds list received is either null or empty." +
                    "Hence it will not be processed further.");
            metrics.updateNorClosedThresholdProcessingErrors();
            return result;
        }

        for (ThresholdsClosedProtos.InstanceThreshold instanceThreshold : instanceThresholdList) {
            if (StringUtils.isEmpty(instanceThreshold.getAccountId())) {
                log.error("Validation of NOR closed threshold data failed. Reason: Account identifier is " +
                        "either invalid or undefined. Hence it will not be be processed further. Details: {}", instanceThreshold);
                metrics.updateNorClosedThresholdProcessingErrors();
                continue;
            }

            if (StringUtils.isEmpty(instanceThreshold.getThresholdType())) {
                log.error("Validation of NOR closed threshold data failed. Reason: Threshold type is " +
                        "either invalid or undefined. Hence it will not be be processed further. Details: {}", instanceThreshold);
                metrics.updateNorClosedThresholdProcessingErrors();
                continue;
            }

            if (StringUtils.isEmpty(instanceThreshold.getInstanceId()) && StringUtils.isEmpty(instanceThreshold.getTransactionId())) {
                log.error("Validation of NOR closed threshold data failed. Reason: Both instance Id and Threshold Id are either invalid or undefined. " +
                        "Hence it will not be processed further. Details: {}", instanceThreshold);
                metrics.updateNorClosedThresholdProcessingErrors();
                continue;
            }

            if (!StringUtils.isEmpty(instanceThreshold.getInstanceId())) {
                String serviceIdentifier = instanceThreshold.getServiceId();
                if (StringUtils.isEmpty(serviceIdentifier)) {
                    List<BasicEntity> serviceList = cacheWrapper.getServicesMappedToInstance(instanceThreshold.getAccountId(), instanceThreshold.getInstanceId());
                    if (StringUtils.isEmpty(serviceList)) {
                        log.error("Validation of NOR closed threshold data failed. Reason: No Service details list found for instance [{}]. " +
                                "Hence it will not be processed further. Details: {}", instanceThreshold.getInstanceId(), instanceThreshold);
                        metrics.updateNorClosedThresholdProcessingErrors();
                        continue;
                    }
                    serviceIdentifier = serviceList.get(0).getIdentifier();
                }

                String applicationIdentifier = instanceThreshold.getApplicationId();
                if (StringUtils.isEmpty(applicationIdentifier)) {
                    List<BasicEntity> applicationList = cacheWrapper.getApplicationsMappedToService(instanceThreshold.getAccountId(), serviceIdentifier);
                    if (StringUtils.isEmpty(applicationList)) {
                        log.error("Validation of NOR closed threshold data failed. Reason: No Application details list found for service [{}], instance [{}]. " +
                                "Hence it will not be processed further. Details: {}", serviceIdentifier, instanceThreshold.getInstanceId(), instanceThreshold);
                        metrics.updateNorClosedThresholdProcessingErrors();
                        continue;
                    }
                    applicationIdentifier = applicationList.get(0).getIdentifier();
                }

                instanceThreshold = instanceThreshold.toBuilder()
                        .setServiceId(serviceIdentifier)
                        .setApplicationId(applicationIdentifier)
                        .build();

            } else if (!StringUtils.isEmpty(instanceThreshold.getTransactionId())) {
                String serviceIdentifier = instanceThreshold.getServiceId();
                if (StringUtils.isEmpty(serviceIdentifier)) {
                    Transaction transaction = cacheWrapper.getTransactionDetails(instanceThreshold.getAccountId(), instanceThreshold.getTransactionId());
                    if (transaction == null) {
                        log.error("Validation of NOR closed threshold data failed. Reason: Transaction details is null. " + "Hence it will not be processed further. Details: {}", instanceThreshold);
                        metrics.updateNorClosedThresholdProcessingErrors();
                        continue;
                    }

                    BasicEntity basicEntity = cacheWrapper.getServiceDetailsFromServiceId(instanceThreshold.getAccountId(), transaction.getServiceId());
                    if (basicEntity == null) {
                        log.error("Validation of NOR closed threshold data failed. Reason: Service Details is null for service id {} mapped to transaction {}. " + "Hence it will not be processed further. Details: {}", transaction.getServiceId(), transaction.getIdentifier(), instanceThreshold);
                        metrics.updateNorClosedThresholdProcessingErrors();
                        continue;
                    }
                    serviceIdentifier = basicEntity.getIdentifier();
                }

                String applicationIdentifier = instanceThreshold.getApplicationId();
                if (StringUtils.isEmpty(applicationIdentifier)) {
                    List<BasicEntity> applicationList = cacheWrapper.getApplicationsMappedToService(instanceThreshold.getAccountId(), serviceIdentifier);
                    if (StringUtils.isEmpty(applicationList)) {
                        log.error("Validation of NOR closed threshold data failed. Reason: No Application details list found for service [{}], transaction [{}]. " +
                                "Hence it will not be processed further. Details: {}", serviceIdentifier, instanceThreshold.getTransactionId(), instanceThreshold);
                        metrics.updateNorClosedThresholdProcessingErrors();
                        continue;
                    }
                    applicationIdentifier = applicationList.get(0).getIdentifier();
                }

                instanceThreshold = instanceThreshold.toBuilder()
                        .setServiceId(serviceIdentifier)
                        .setApplicationId(applicationIdentifier)
                        .build();
            }

            List<ThresholdsClosedProtos.KPIInfo> kpiInfoList = instanceThreshold.getKpiInfoList();
            for (ThresholdsClosedProtos.KPIInfo kpiInfo : kpiInfoList) {
                if (StringUtils.isEmpty(kpiInfo.getKpiId())) {
                    log.error("Validation of NOR closed threshold data failed. Reason: KPI Id is either invalid or undefined. " +
                            "Hence it will not be processed further. Details: {}", instanceThreshold);
                    metrics.updateNorClosedThresholdProcessingErrors();
                }
            }
            finalInstanceThresholdList.add(instanceThreshold);
        }

        log.trace("List of Instance thresholds object that passed validation are {}", finalInstanceThresholdList);

        if (finalInstanceThresholdList.isEmpty()) {
            log.error("None of the instance threshold objects from the proto passed the validation. Please check logs for more details.");
            metrics.updateNorClosedThresholdProcessingErrors();
            return result;
        }

        ThresholdsClosedProtos.NORThresholdsClosed.Builder builder = norThresholdsClosed.toBuilder();
        builder.clearInstanceThreshold();
        builder.addAllInstanceThreshold(finalInstanceThresholdList);
        ThresholdsClosedProtos.NORThresholdsClosed updatedNORThresholdsClosed = builder.build();

        result.setValid(true);
        result.setProto(updatedNORThresholdsClosed);
        return result;
    }
}
