package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.appnomic.appsone.common.protbuf.ViolatedEventProtos;
import com.heal.event.detector.pojos.GenericValidationObject;
import com.heal.event.detector.pojos.ViolatedData;
import com.heal.event.detector.pojos.enums.ViolationEventType;
import com.heal.event.detector.repo.OpenSearchRepo;
import com.heal.event.detector.utility.HealthMetrics;
import com.heal.event.detector.utility.StringUtils;
import com.heal.event.detector.utility.Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class AnomalyEventsProcess {

    @Autowired
    OpenSearchRepo openSearchRepo;

    @Autowired
    PrepareAnomalyData prepareAnomalyData;

    @Autowired
    PersistenceSuppression persistenceSuppression;

    @Autowired
    PerSuppProcessWatcherKPI perSuppProcessWatcherKPI;

    @Autowired
    PerSuppBatchJob perSuppBatchJob;

    @Autowired
    HealthMetrics metrics;

    @Value("${kpi.data.outoforder.mins:10}")
    int outOfOrderValue;

    public List<AnomalyEventProtos.AnomalyEvent> processViolatedKpiData(List<ViolatedData> violatedDataList) {
        sinkRawViolationToOS(violatedDataList);

        // Removed the anomalyKpisList saving the anomalies within persistenceSuppression.applyPersistenceSuppressionCreateUpdateAnomaly()
//        List<AnomalyEventProtos.AnomalyEvent> anomalyKpisList = persistenceAndSuppressionOnViolatedData(violatedDataList);
        persistenceAndSuppressionOnViolatedData(violatedDataList);
        List<AnomalyEventProtos.AnomalyEvent> anomalyWatcherKpisList = processWatcherKpis(violatedDataList);
        List<AnomalyEventProtos.AnomalyEvent> anomalyBatchJobKpisList = processBatchJobKpis(violatedDataList);

        List<AnomalyEventProtos.AnomalyEvent> anomalyKpisList = Stream.of(anomalyWatcherKpisList, anomalyBatchJobKpisList)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());

        openSearchRepo.insertAnomalies(prepareAnomalyData.getAllAnomalies(anomalyKpisList));
        return anomalyKpisList;
    }

    private void sinkRawViolationToOS(List<ViolatedData> violatedDataList) {

        try {
            if (violatedDataList.isEmpty()) {
                return;
            }
            Map<ViolationEventType, List<ViolatedData>> violationEventTypeListMap = violatedDataList.parallelStream()
                    .filter(c -> !Objects.isNull(c.getEventType()))
                    .collect(Collectors.groupingBy(ViolatedData::getEventType));

            if (violationEventTypeListMap.isEmpty()) {
                return;
            }

            openSearchRepo.insertRawKpiViolations(violationEventTypeListMap
                    .getOrDefault(ViolationEventType.KPI_VIOLATION, new ArrayList<>()));

            openSearchRepo.insertRawTxnViolations(violationEventTypeListMap
                    .getOrDefault(ViolationEventType.TXN_VIOLATION, new ArrayList<>()));

            openSearchRepo.insertRawBatchJobViolations(violationEventTypeListMap
                    .getOrDefault(ViolationEventType.BATCH_JOB_VIOLATION, new ArrayList<>()));
        } catch (Exception e) {
            log.error("Exception while pushing data into OS. Details: ", e);
        }
    }

    private void persistenceAndSuppressionOnViolatedData(List<ViolatedData> violatedDataList) {
        violatedDataList.parallelStream()
                .filter(c -> c.getEventType() != ViolationEventType.BATCH_JOB_VIOLATION)
                .filter(c -> !c.getAppIds().isEmpty())
                .filter(c -> !((c.getEventType() == ViolationEventType.KPI_VIOLATION)
                        && (c.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.FileWatch
                        || c.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.ConfigWatch)))
                .map(value -> persistenceSuppression.checkAndApplyPersistenceSuppression(value))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<AnomalyEventProtos.AnomalyEvent> processBatchJobKpis(List<ViolatedData> violatedDataList) {
        return violatedDataList.parallelStream()
                .filter(c -> c.getEventType() == ViolationEventType.BATCH_JOB_VIOLATION)
                .filter(c -> !c.getAppIds().isEmpty())
                .map(value -> perSuppBatchJob.applyPersistenceSupp(value))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<AnomalyEventProtos.AnomalyEvent> processWatcherKpis(List<ViolatedData> violatedDataList) {
        return violatedDataList.parallelStream()
                .filter(c -> c.getEventType() != ViolationEventType.BATCH_JOB_VIOLATION)
                .filter(c -> c.getKpiType() != null &&
                        (c.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.FileWatch
                                || c.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.ConfigWatch))
                .map(value -> perSuppProcessWatcherKPI.applyPersistenceSuppressionBasedOnType(value,
                        0, 0, false, value.getServiceList().get(0), 1))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public GenericValidationObject<ViolatedEventProtos.ViolatedEvent> validateAnomalyEvent(ViolatedEventProtos.ViolatedEvent violatedEvent) {
        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> result =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .isValid(false)
                        .proto(violatedEvent)
                        .build();

        if (violatedEvent == null) {
            log.error("Validation of violated event data failed. Reason: Data received is null." +
                    "Hence it will not be processed further.");
            metrics.updateViolatedEventProcessingErrors();
            return result;
        }

        if (violatedEvent.getSerializedSize() == 0) {
            log.error("Validation of violated event data failed. Reason: Data received is either invalid or undefined." +
                    "Hence it will not be processed further.");
            metrics.updateViolatedEventProcessingErrors();
            return result;
        }

        if (Utils.isOutOfOrder(violatedEvent.getViolationTmeInGMT(), outOfOrderValue)) {
            log.error("Validation of violated event data failed. Reason: Data received is OutOfOrder for KPIs:{}, Time:{}, OutOfOrderValue:{}." +
                    "Hence it will not be processed further.", violatedEvent.getKpisList().size(), violatedEvent.getViolationTmeInGMT(), outOfOrderValue);
            metrics.updateViolatedEventProcessingErrors();
            return result;
        }

        if (StringUtils.isEmpty(violatedEvent.getAccountId())) {
            log.error("Validation of violated event data failed. Reason: Account identifier is " +
                    "either invalid or undefined. Hence it will not be be processed further.");
            metrics.updateViolatedEventProcessingErrors();
            return result;
        }

        if (violatedEvent.getAppIdList().isEmpty()) {
            log.error("Validation of violated event data failed. Reason: Application identifier list is " +
                    "empty. Hence it will not be be processed further.");
            metrics.updateViolatedEventProcessingErrors();
            return result;
        }

        List<String> nonEmptyAppIdList = violatedEvent.getAppIdList().stream()
                .filter(c -> !StringUtils.isEmpty(c)).collect(Collectors.toList());
        if (nonEmptyAppIdList.isEmpty()) {
            log.error("Validation of violated event data failed. Reason: Application identifier is " +
                    "either invalid or undefined. Hence it will not be be processed further.");
            metrics.updateViolatedEventProcessingErrors();
            return result;
        }

        if (nonEmptyAppIdList.size() < violatedEvent.getAppIdList().size()) {
            violatedEvent = violatedEvent.toBuilder().clearAppId().addAllAppId(nonEmptyAppIdList).build();
            result.setProto(violatedEvent);
        }

        if (StringUtils.isEmpty(violatedEvent.getThresholdType())) {
            log.error("Validation of violated event data failed. Reason: Threshold type is " +
                    "either invalid or undefined. Hence it will not be be processed further.");
            metrics.updateViolatedEventProcessingErrors();
            return result;
        }

        if (violatedEvent.getKpisList().isEmpty() && violatedEvent.getBatchJobList().isEmpty() &&
                violatedEvent.getTransactionsList().isEmpty()) {
            log.error("Validation of violated event data failed. Reason: Kpi list, Transaction list and" +
                    " BatchJob list are empty. Hence it will not be be processed further.");
            metrics.updateViolatedEventProcessingErrors();
            return result;
        }

        List<ViolatedEventProtos.Kpi> kpiList = new ArrayList<>();
        for (ViolatedEventProtos.Kpi kpi : violatedEvent.getKpisList()) {

            if (StringUtils.isEmpty(kpi.getKpiInfo().getKpiId())) {
                log.error("Validation of violated event data failed. Reason: Kpi id in kpi info is " +
                        "either invalid or undefined. Hence it will not be be processed further.");
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            String kpiId = kpi.getKpiInfo().getKpiId();

            if (StringUtils.isEmpty(kpi.getKpiInfo().getInstanceId())) {
                log.error("Validation of violated event data failed. Reason: Instance id for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(kpi.getKpiInfo().getKpiAttribute())) {
                log.error("Validation of violated event data failed. Reason: Kpi Attribute for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(kpi.getKpiInfo().getOperationType())) {
                log.error("Validation of violated event data failed. Reason: Operation type in kpi info for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (kpi.getKpiInfo().getSvcIdList().isEmpty()) {
                log.error("Validation of violated event data failed. Reason: Service identifier list for kpi [{}] is " +
                        "empty. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (kpi.getKpiInfo().getThresholdsMap().isEmpty()) {
                log.error("Validation of violated event data failed. Reason: Threshold map in kpi info for kpi [{}] is " +
                        "empty. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(kpi.getValue())) {
                log.error("Validation of violated event data failed. Reason: Kpi value in kpi info for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            List<String> nonEmptyServiceIdList = kpi.getKpiInfo().getSvcIdList().stream()
                    .filter(c -> !StringUtils.isEmpty(c)).collect(Collectors.toList());
            if (nonEmptyServiceIdList.isEmpty()) {
                log.error("Validation of violated event data failed. Reason: Service identifier for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (nonEmptyServiceIdList.size() <= kpi.getKpiInfo().getSvcIdList().size()) {
                kpiList.add(kpi.toBuilder().clearKpiInfo().setKpiInfo(kpi.getKpiInfo().toBuilder().clearSvcId().addAllSvcId(nonEmptyServiceIdList).build()).build());
            }

        }
        violatedEvent = violatedEvent.toBuilder().clearKpis().addAllKpis(kpiList).build();
        result.setProto(violatedEvent);

        for (ViolatedEventProtos.Transaction txn : violatedEvent.getTransactionsList()) {

            if (StringUtils.isEmpty(txn.getTxnInfo().getKpiId())) {
                log.error("Validation of violated event data failed. Reason: Kpi id in transaction info is " +
                        "either invalid or undefined. Hence it will not be be processed further.");
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            String kpiId = txn.getTxnInfo().getKpiId();

            if (StringUtils.isEmpty(txn.getTxnInfo().getTransactionId())) {
                log.error("Validation of violated event data failed. Reason: Transaction id for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(txn.getTxnInfo().getGroupId())) {
                log.error("Validation of violated event data failed. Reason: Group id for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(txn.getTxnInfo().getOperationType())) {
                log.error("Validation of violated event data failed. Reason: Operation type in transaction info for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(txn.getTxnInfo().getSvcId())) {
                log.error("Validation of violated event data failed. Reason: Service identifier in transaction info for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (txn.getTxnInfo().getThresholdsMap().isEmpty()) {
                log.error("Validation of violated event data failed. Reason: Threshold map in transaction info for kpi [{}] is " +
                        "empty. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(txn.getValue())) {
                log.error("Validation of violated event data failed. Reason: Kpi value in transaction info for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(txn.getTxnInfo().getResponseTimeType().name())) {
                log.error("Validation of violated event data failed. Reason: Response time type in transaction info for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

        }

        for (ViolatedEventProtos.BatchJob batchJob : violatedEvent.getBatchJobList()) {

            if (StringUtils.isEmpty(batchJob.getKpiId())) {
                log.error("Validation of violated event data failed. Reason: Kpi id in batchjob info is " +
                        "either invalid or undefined. Hence it will not be be processed further.");
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            String kpiId = batchJob.getKpiId();

            if (StringUtils.isEmpty(batchJob.getBatchJob())) {
                log.error("Validation of violated event data failed. Reason: Batchjob id for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(batchJob.getOperationType())) {
                log.error("Validation of violated event data failed. Reason: Operation type in batchjob info for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (batchJob.getThresholdsMap().isEmpty()) {
                log.error("Validation of violated event data failed. Reason: Threshold map in batchjob info for kpi [{}] is " +
                        "empty. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(batchJob.getValue())) {
                log.error("Validation of violated event data failed. Reason: Kpi value in batchjob info for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }
        }

        log.info("Violated Event validated successfully.");
        result.setValid(true);
        return result;
    }
}
