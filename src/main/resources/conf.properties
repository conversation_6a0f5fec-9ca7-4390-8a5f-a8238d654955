# ==========================================================
# RabbitMQ server configuration
# Basic configuration needed to communicate with RabbitMQ.
# HTTP mode of communication will be utilized in this case.
# ==========================================================
spring.rabbitmq.addresses=rabbitmq.appnomic:5672
spring.rabbitmq.username=guest
spring.rabbitmq.password=
spring.rabbitmq.ssl.enabled=true
spring.rabbitmq.ssl.algorithm=TLSv1.3
spring.rabbitmq.aggregatedKpiInputQueueName=static-aggregated-kpi
spring.rabbitmq.violatedEventInputQueueName=violated-events
spring.rabbitmq.anomalyOutputSignalQueueName=anomaly-event-signal-messages
spring.rabbitmq.anomalyOutputActionQueueName=anomaly-event-action-messages
spring.rabbitmq.norThresholdQueueName=nor-thresholds
spring.rabbitmq.aggregatedKpi.prefetchCount=100
spring.rabbitmq.aggregatedKpi.acknowledgementMode=AUTO
spring.rabbitmq.aggregatedKpi.concurrentConsumerSize=10
spring.rabbitmq.violatedEvent.prefetchCount=10
spring.rabbitmq.violatedEvent.acknowledgementMode=AUTO
spring.rabbitmq.violatedEvent.concurrentConsumerSize=1
spring.rabbitmq.norThreshold.prefetchCount=10
spring.rabbitmq.norThreshold.acknowledgementMode=AUTO
spring.rabbitmq.norThreshold.concurrentConsumerSize=1
spring.rabbitmq.thresholdForAggregatedKPIInSecs=4
spring.rabbitmq.thresholdForViolatedEventInSecs=4
spring.rabbitmq.thresholdForNorThresholdInSecs=4
spring.rabbitmq.norClosedThresholdQueueName=nor-thresholds-closed

# ==========================================================
#Redis Server Configuration
# ==========================================================
spring.redis.cluster.nodes=redis-node1.appnomic:9001,redis-node1.appnomic:9002,redis-node2.appnomic:9001,redis-node2.appnomic:9002,redis-node3.appnomic:9001,redis-node3.appnomic:9002
spring.redis.ssl=true
spring.redis.max.idle.connections=500
spring.redis.min.idle.connections=500
spring.redis.max.total.connections=500
spring.redis.max.wait.secs=20
spring.redis.share.native.connection=false
spring.redis.username=
spring.redis.password=

# ==========================================================
# Thread Pool Configuration
# ==========================================================
thread.pool.core.size=100
thread.pool.max.size=200

# ==========================================================
# Miscellaneous
# batch.job.collection.interval in secs
# opensearch.data.push.schedule.initial.delay in secs
# opensearch.data.push.schedule.interval in secs
# ==========================================================
batch.job.suppression.value=3
batch.job.collection.interval=60
signal.severity.high=Severe
signal.severity.low=Default
signal.severity.id.high=433
signal.severity.id.medium=432
signal.severity.id.low=431
heal.transaction.component.identifier=Transaction
heal.batch.component.identifier=BatchProcess
heal.global.account.identifier=e573f852-5057-11e9-8fd2-b37b61e52317
opensearch.data.push.schedule.initial.delay=2
opensearch.data.push.schedule.interval=5
opensearch.batch.size=500
opensearch.batch.queue.max.size=10000
offset.from.gmt=********

# ==========================================================
# Violated Event Out Of Order Details
# ==========================================================
kpi.data.outoforder.mins=15

# ==========================================================
# OpenSearch Details
# ==========================================================
opensearch.nodes=opensearch-node1.appnomic:9200
opensearch.username=admin
opensearch.password=
opensearch.protocol=http
opensearch.kpi.violations.index=heal_kpi_violations
opensearch.transaction.violations.index=heal_transaction_violations
opensearch.batchjob.violations.index=heal_batch_job_violations
opensearch.anomalies.index=heal_anomalies
opensearch.kpi.thresholds.index=heal_instance_kpi_thresholds
opensearch.transaction.thresholds.index=heal_transaction_kpi_thresholds

# ==========================================================
# Health Metrics Details
# ==========================================================
health.metrics.update.interval.milliseconds=60000
health.metrics.log.interval.milliseconds=10000
management.endpoints.web.exposure.include=*
management.endpoints.jmx.exposure.include=*
management.endpoint.health.enabled=true
management.endpoints.web.base-path=/measure
management.server.port=8989
spring.jmx.enabled=true

html.encoder.enable=1

nor.closed.thresholds.duration.days=2
forward.raw.anomalies=false