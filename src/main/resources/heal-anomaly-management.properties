# Heal Anomaly Management Configuration
# =====================================

# Enable/Disable Heal Anomaly Management
heal.anomaly.management.enabled=true

# Enable dual index mode (both legacy and new indexes)
heal.anomaly.dual.index.mode=true

# Keep legacy support for backward compatibility
heal.anomaly.legacy.support=true

# OpenSearch Index Configuration
# ==============================

# Heal Anomaly Index Prefixes
opensearch.heal.anomalies.index.prefix=heal_anomalies
opensearch.heal.alerts.index.prefix=heal_alerts

# Legacy Index (for backward compatibility)
opensearch.anomalies.index=anomalies

# Anomaly Management Queue Configuration
# =====================================

# Queue Names
spring.rabbitmq.anomalyCreateQueueName=anomaly-create
spring.rabbitmq.anomalyUpdateQueueName=anomaly-update
spring.rabbitmq.anomalyCloseQueueName=anomaly-close
spring.rabbitmq.anomalyManagementQueueName=anomaly-management

# Queue Consumer Configuration
spring.rabbitmq.anomaly.management.consumers=2
spring.rabbitmq.anomaly.management.prefetch=10
spring.rabbitmq.anomaly.management.ack.mode=AUTO

# Performance Configuration
# =========================

# Batch processing settings
heal.anomaly.batch.size=100
heal.anomaly.batch.timeout.ms=5000

# Retry configuration
heal.anomaly.retry.max.attempts=3
heal.anomaly.retry.delay.ms=1000

# Circuit breaker configuration
heal.anomaly.circuit.breaker.enabled=true
heal.anomaly.circuit.breaker.failure.threshold=5
heal.anomaly.circuit.breaker.timeout.ms=30000

# Monitoring Configuration
# ========================

# Enable metrics collection
heal.anomaly.metrics.enabled=true

# Health check configuration
heal.anomaly.health.check.enabled=true
heal.anomaly.health.check.interval.ms=30000

# Logging Configuration
# ====================

# Log levels for anomaly management
logging.level.com.heal.event.detector.service.AnomalyManagementService=DEBUG
logging.level.com.heal.event.detector.core.AnomalyEventsProcess=DEBUG
logging.level.com.heal.event.detector.repo.OpenSearchRepo=INFO

# Feature Flags
# =============

# Enable specific features
heal.anomaly.feature.auto.close.enabled=false
heal.anomaly.feature.escalation.enabled=false
heal.anomaly.feature.notification.enabled=true

# Data Retention Configuration
# ============================

# Index retention settings
heal.anomaly.index.retention.days=90
heal.anomaly.alert.index.retention.days=30

# Cleanup job configuration
heal.anomaly.cleanup.enabled=true
heal.anomaly.cleanup.cron=0 0 2 * * ?

# Security Configuration
# ======================

# API security settings
heal.anomaly.api.auth.enabled=false
heal.anomaly.api.rate.limit.enabled=true
heal.anomaly.api.rate.limit.requests.per.minute=100

# Integration Configuration
# =========================

# External system integration
heal.anomaly.external.notification.enabled=false
heal.anomaly.external.webhook.url=
heal.anomaly.external.webhook.timeout.ms=5000

# Development/Testing Configuration
# =================================

# Test mode settings
heal.anomaly.test.mode=false
heal.anomaly.test.mock.external.services=false

# Debug settings
heal.anomaly.debug.log.requests=false
heal.anomaly.debug.log.responses=false
heal.anomaly.debug.performance.tracking=true
