# 🔍 OpenSearch Anomaly Indexing - Debugging Guide

## 🚨 **ISSUE DESCRIPTION**

You're seeing log entries like:
```
Anomaly data indexed in OpenSearch for anomalyId: {}, account: {}
```

But the data is **NOT appearing** in the OpenSearch server. This guide will help you debug and identify where the data is getting lost.

---

## 🔧 **ENHANCED LOGGING ADDED**

I've added comprehensive logging throughout the anomaly indexing pipeline to help identify exactly where the issue occurs.

### **1. Enhanced `insertOrUpdateAnomalyData` Method**

The method now logs detailed information at each step:

```java
🔍 [ANOMALY_INDEXING] Starting indexing process for anomalyId: {anomalyId}
🔍 [ANOMALY_INDEXING] Account: {account}, AnomalyTime: {time}, WeekString: {week}
🔍 [ANOMALY_INDEXING] Target Index: {indexName}
🔍 [ANOMALY_INDEXING] Base anomaliesIndex config: {baseIndex}
✅ [ANOMALY_INDEXING] OpenSearch client available for account: {account}
🔍 [ANOMALY_INDEXING] Converted anomaly document - ID: {id}, KpiId: {kpiId}, Value: {value}
🔍 [ANOMALY_INDEXING] Created IndexRequest - Index: {index}, ID: {id}, Document: Valid
🔍 [ANOMALY_INDEXING] Queue status - Current: {current}, Max: {max}, Available: {available}
✅ [ANOMALY_INDEXING] Successfully queued for indexing - AnomalyId: {id}, Account: {account}, Index: {index}
```

### **2. Enhanced Queue Processing**

The `OSDataPushScheduler` now logs queue operations:

```java
📥 [OS_QUEUE] Added to index queue - Account: {account}, OriginalIndex: {original}, FinalIndex: {final}, ID: {id}
🔄 [OS_BULK] Processing index request - Account: {account}, Zone: {zone}, Index: {index}, ID: {id}
✅ [OS_BULK] OpenSearch client obtained for account: {account}, zone: {zone}
📋 [OS_BULK] Created bulk request for index: {index}, document ID: {id}
```

### **3. Enhanced Bulk Operations**

The bulk indexing now provides detailed success/failure information:

```java
🚀 [OS_BULK] Executing bulk request to OpenSearch...
✅ [OS_BULK] Bulk indexing operation completed successfully! Items processed: {count}
✅ [OS_BULK] Successfully indexed - Index: {index}, ID: {id}, Result: {result}
❌ [OS_BULK] Index operation failed - Index: {index}, ID: {id}, Type: {type}, Reason: {reason}
```

---

## 🕵️ **DEBUGGING STEPS**

### **Step 1: Check Application Logs**

Look for the new log patterns to trace the data flow:

1. **Anomaly Creation**: Look for `🔍 [ANOMALY_INDEXING] Starting indexing process`
2. **Queue Addition**: Look for `📥 [OS_QUEUE] Added to index queue`
3. **Bulk Processing**: Look for `🔄 [OS_BULK] Processing index request`
4. **OpenSearch Execution**: Look for `🚀 [OS_BULK] Executing bulk request`
5. **Final Result**: Look for `✅ [OS_BULK] Successfully indexed` or `❌ [OS_BULK] Index operation failed`

### **Step 2: Use Debug Method**

I've added a debug method you can call to test OpenSearch connectivity:

```java
@Autowired
private OpenSearchRepo openSearchRepo;

// Call this method to debug OpenSearch connection
openSearchRepo.debugOpenSearchConnection("your-account-identifier");
```

This will log:
```
🔧 [OS_DEBUG] Starting OpenSearch connection debug for account: {account}
✅ [OS_DEBUG] OpenSearch client created successfully
🏓 [OS_DEBUG] Ping result: true/false
📋 [OS_DEBUG] Configuration details:
   - Base anomaliesIndex: heal_anomalies
   - Account: your-account
   - Queue size: 5/50000
   - Generated index name: heal_anomalies_account_2025.w09
   - Index exists: true/false
```

### **Step 3: Check Configuration**

Verify these configuration values in your logs:

1. **Base Index Name**: Should match your `opensearch.anomalies.index` property
2. **Generated Index Name**: Should follow pattern `{baseIndex}_{account}_{year}.w{week}`
3. **OpenSearch Client**: Should not be NULL
4. **Queue Status**: Should not be full

---

## 🎯 **COMMON ISSUES & SOLUTIONS**

### **Issue 1: NULL OpenSearch Client**
**Symptoms**: `❌ [OS_BULK] NULL OpenSearch client for account`
**Solutions**:
- Check OpenSearch server connectivity
- Verify account configuration in cache
- Check zone mapping configuration

### **Issue 2: Queue Full**
**Symptoms**: `❌ [OS_QUEUE] Failed to add to index queue - Queue full!`
**Solutions**:
- Increase `opensearch.batch.queue.max.size` property
- Check if OpenSearch server is responding slowly
- Monitor queue processing rate

### **Issue 3: Index Creation Failure**
**Symptoms**: `❌ [OS_BULK] Index operation failed`
**Solutions**:
- Check OpenSearch server disk space
- Verify index template/mapping
- Check OpenSearch server logs
- Verify account has permission to create indexes

### **Issue 4: Wrong Index Name**
**Symptoms**: Data goes to wrong index
**Solutions**:
- Verify `anomalyTime` is correct
- Check week calculation logic
- Verify account identifier

### **Issue 5: Network/Connection Issues**
**Symptoms**: Timeouts or connection errors
**Solutions**:
- Check network connectivity to OpenSearch
- Verify OpenSearch server is running
- Check firewall/security group settings

---

## 📊 **MONITORING CHECKLIST**

### **Before Deployment**
- [ ] Enable debug logging: `logging.level.com.heal.event.detector.repo.OpenSearchRepo=INFO`
- [ ] Verify OpenSearch server is accessible
- [ ] Check queue size configuration
- [ ] Test with debug method

### **After Deployment**
- [ ] Monitor for `🔍 [ANOMALY_INDEXING]` log entries
- [ ] Check queue size: `📥 [OS_QUEUE]` entries
- [ ] Verify bulk operations: `✅ [OS_BULK]` success entries
- [ ] Monitor error rates: `❌` entries

### **OpenSearch Server Checks**
- [ ] Check server logs for incoming requests
- [ ] Verify index creation: `GET /_cat/indices/heal_anomalies*`
- [ ] Check document count: `GET /heal_anomalies_*/_count`
- [ ] Monitor server resources (CPU, memory, disk)

---

## 🔧 **CONFIGURATION VERIFICATION**

### **Required Properties**
```properties
# Base index name
opensearch.anomalies.index=heal_anomalies

# Queue configuration
opensearch.batch.queue.max.size=50000
opensearch.batch.size=500

# OpenSearch connection
opensearch.nodes=your-opensearch-server:9200
opensearch.username=your-username
opensearch.password=your-password
```

### **Index Naming Pattern**
- **Pattern**: `{baseIndex}_{accountId}_{year}.w{weekNumber}`
- **Example**: `heal_anomalies_prod-account-123_2025.w09`

---

## 🚀 **NEXT STEPS**

1. **Deploy** the enhanced logging version
2. **Monitor** the new log patterns
3. **Use** the debug method to test connectivity
4. **Check** OpenSearch server directly for incoming requests
5. **Verify** index creation and document insertion

The enhanced logging will help you pinpoint exactly where in the pipeline the data is getting lost, making it much easier to identify and fix the root cause.
