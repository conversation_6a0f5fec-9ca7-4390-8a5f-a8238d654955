<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds">
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/tmp/logs/event-detector.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>/tmp/logs/event-detector_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>{{ key "service/eventdetector/logs/maxfilesize" }}</maxFileSize>
            <totalSizeCap>{{ key "service/eventdetector/logs/totalsizecap" }}</totalSizeCap>
            <maxHistory>{{ key "service/eventdetector/logs/maxhistory" }}</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="CORE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/tmp/logs/event-detector-core.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>/tmp/logs/event-detector-core_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>{{ key "service/eventdetector/logs/maxfilesize" }}</maxFileSize>
            <totalSizeCap>{{ key "service/eventdetector/logs/totalsizecap" }}</totalSizeCap>
            <maxHistory>{{ key "service/eventdetector/logs/maxhistory" }}</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="STATS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/tmp/logs/event-detector-stats.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>/tmp/logs/event-detector-stats_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>{{ key "service/eventdetector/auditlogs/maxfilesize" }}</maxFileSize>
            <totalSizeCap>{{ key "service/eventdetector/auditlogs/totalsizecap" }}</totalSizeCap>
            <maxHistory>{{ key "service/eventdetector/auditlogs/maxhistory" }}</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.heal.event.detector" level="{{ key "service/eventdetector/loglevel" }}" additivity="false">
        <appender-ref ref="FILE"/>
    </logger>

    <logger name="com.heal.event.detector.scheduler.StatisticsScheduler" level="{{ key "service/eventdetector/statsloglevel" }}" additivity="false">
        <appender-ref ref="STATS_FILE"/>
    </logger>

    <root level = "{{ key "service/eventdetector/rootloglevel" }}">
        <appender-ref ref = "CORE_FILE"/>
    </root>

</configuration>