pipeline {
    agent { label 'Second_Slave' }
    tools {
        jdk 'openjdk-17-second'
        maven 'Maven_388'
    }
    environment {
        NEXUS_COMMON_CREDS = credentials('0981d455-e100-4f93-9faf-151ac7e29d8a')
        NEXUS_URL = 'http://*************:8081'
    }

    options {
        buildDiscarder(logRotator(numToKeepStr: '5'))
	    office365ConnectorWebhooks([[
                name: '<PERSON>',
                notifyBackToNormal: true,
                notifyFailure: true,
                notifySuccess: true,
                notifyUnstable: true,
                url: "https://healsoftwareai.webhook.office.com/webhookb2/78345e71-2972-44c4-a270-fbae82662bf1@55dca2af-e23a-4402-b9a6-8833b28a02dc/JenkinsCI/7958868126734afeb78edb01dafdcc05/6fed72e3-b7dd-422f-9075-e6d96468feb0"
            ]]
        )
    }

    parameters {
        gitParameter branchFilter: 'origin/(.*)', defaultValue: 'master', name: '<PERSON><PERSON><PERSON>', type: 'PT_BRANCH_TAG'
    }

    stages {
        stage('Cleanup') {
            steps {
                cleanWs()
            }
        }
        stage('Checkout') {
            steps {
                script {
                    currentBuild.displayName = "#${BUILD_NUMBER}->${params.BRANCH}"
                    currentBuild.description = "Branch: ${params.BRANCH} is used for this build"
                }
                git branch: "${params.BRANCH}", url: 'https://bitbucket.org/appsone/event-detector.git', credentialsId: "fd197b00-fd06-4632-a018-36134111e086"
            }
        }
        stage('Build') {
            steps {
                withSonarQubeEnv('sonarqube_71') {
                    sh 'mvn clean install sonar:sonar'
                }
            }
        }
        stage('Archive Builds') {
            steps {
		        sh "mv target/heal-event-detector*.tar.gz heal-event-detector.tar.gz"
                archiveArtifacts artifacts: 'heal-event-detector.tar.gz', fingerprint: true
            }
        }
        stage('Docker build') {
            steps {
                sh "tar -xvf heal-event-detector.tar.gz"
                script {
                    pom = readMavenPom file: 'pom.xml'
                    version = pom.version
                }
                echo "Building project in version: ${version}"
                sh "docker build -t heal-event-detector:${version} ."
            }
        }
        stage('Publish Docker Image') {
            steps {
                sh "docker save heal-event-detector:${version} > heal-event-detector_${version}.tar"
                sh "curl -v -u ${NEXUS_COMMON_CREDS} --upload-file heal-event-detector_${version}.tar ${NEXUS_URL}/nexus/repository/tls_docker_images/heal-event-detector_${version}.tar"
                sh "echo heal-event-detector_${version} > /tmp/heal-event-detector_version"
            }
        }
        stage('Docker Vapt scan') {
            when {
            expression { params.BRANCH == 'develop' }
                 }
            steps {
                sh "docker scan heal-event-detector:${version} > heal-event-detector-scanreport.txt | echo"
                sh 'curl -v -u ${NEXUS_COMMON_CREDS} --upload-file heal-event-detector-scanreport.txt ${NEXUS_URL}/nexus/repository/Image_scan_report/heal-event-detector-scanreport.txt --progress-bar'
            }
             post {
                success {
                    emailext attachmentsPattern: 'heal-event-detector-scanreport.txt', body: 'Hi Team,<br><br> Please find the attached VAPT report fyr.<br><br>Kindly close it during next release.<br><br>Regards<br> Heal', subject: 'VAPT Report for heal-event-detector', to: '<EMAIL>'
                }
           }
        }
        stage('Docker-Image Cleanup') {
            steps {
                sh "docker rmi -f heal-event-detector:${version}"
            }
        }
    }
}
